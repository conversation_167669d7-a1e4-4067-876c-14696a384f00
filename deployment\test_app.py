"""
Test script for the FinBERT Flask application.
"""

import requests
import json
import time

def test_flask_app():
    """Test the Flask application endpoints."""
    base_url = "http://localhost:5000"
    
    # Test data
    test_texts = [
        "The company reported record quarterly earnings with revenue growth of 25% year-over-year.",
        "Stock prices fell sharply after the company announced significant losses and layoffs.",
        "The quarterly results were in line with analyst expectations, showing stable performance."
    ]
    
    print("🧪 Testing FinBERT Flask Application")
    print("=" * 50)
    
    # Test health endpoint
    try:
        print("\n1. Testing health endpoint...")
        response = requests.get(f"{base_url}/health")
        if response.status_code == 200:
            health_data = response.json()
            print(f"✅ Health check: {health_data['status']}")
            print(f"   Model loaded: {health_data['model_loaded']}")
        else:
            print(f"❌ Health check failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Health check error: {e}")
    
    # Test API endpoint
    print("\n2. Testing API endpoint...")
    for i, text in enumerate(test_texts, 1):
        try:
            print(f"\n   Test {i}: {text[:50]}...")
            
            start_time = time.time()
            response = requests.post(
                f"{base_url}/api/analyze",
                json={"text": text},
                headers={"Content-Type": "application/json"}
            )
            end_time = time.time()
            
            if response.status_code == 200:
                data = response.json()
                print(f"   ✅ Sentiment: {data['sentiment']}")
                print(f"   ✅ Confidence: {data['confidence']:.1%}")
                print(f"   ✅ Response time: {(end_time - start_time):.2f}s")
                print(f"   ✅ Summary: {data['summary'][:100]}...")
            else:
                print(f"   ❌ API test failed: {response.status_code}")
                print(f"   Error: {response.text}")
                
        except Exception as e:
            print(f"   ❌ API test error: {e}")
    
    # Test web form endpoint
    print("\n3. Testing web form endpoint...")
    try:
        test_text = test_texts[0]
        response = requests.post(
            f"{base_url}/analyze",
            data={"text": test_text}
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print(f"   ✅ Web form test successful")
                print(f"   ✅ Sentiment: {data['sentiment']}")
                print(f"   ✅ Confidence: {data['confidence']:.1%}")
            else:
                print(f"   ❌ Web form test failed: {data.get('error')}")
        else:
            print(f"   ❌ Web form test failed: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Web form test error: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 Testing completed!")
    print("\n💡 To test the web interface:")
    print("   1. Open your browser")
    print("   2. Go to http://localhost:5000")
    print("   3. Enter financial text and click 'Analyze Sentiment'")

if __name__ == "__main__":
    test_flask_app()

# FinBERT Sentiment Analysis Project
# Requirements file with all necessary dependencies

# Core ML libraries
torch>=2.0.0
transformers>=4.30.0
datasets>=2.12.0
tokenizers>=0.13.0
huggingface-hub>=0.15.0
safetensors>=0.3.0

# Data processing
pandas>=1.5.0
numpy>=1.24.0
scikit-learn>=1.3.0

# Visualization
matplotlib>=3.7.0
seaborn>=0.12.0
plotly>=5.14.0

# Web framework (for deployment)
Flask>=2.3.0
Werkzeug>=2.3.0
Jinja2>=3.1.0

# Utilities
tqdm>=4.65.0
requests>=2.31.0
pyyaml>=6.0.0
click>=8.1.0
regex>=2023.8.0

# Development and testing
jupyter>=1.0.0
notebook>=6.5.0
ipywidgets>=8.0.0

# Optional: GPU acceleration
# torch-audio  # Uncomment if needed
# torch-vision  # Uncomment if needed

# Optional: Advanced features
# tensorboard>=2.13.0  # For training visualization
# wandb>=0.15.0  # For experiment tracking
# optuna>=3.2.0  # For hyperparameter optimization

# FinBERT Financial Sentiment Analysis Web Application

## 🌟 Overview

This is an advanced web application that provides financial sentiment analysis using a fine-tuned FinBERT model. The application offers comprehensive analysis including sentiment classification, confidence scores, text summarization, and detailed explanations of why text was classified as positive, negative, or neutral.

## 🚀 Features

### 🧠 Advanced AI Analysis
- **Fine-tuned FinBERT Model**: Specifically trained on financial data for maximum accuracy
- **High Precision**: Optimized for financial news, earnings reports, and market analysis
- **Real-time Analysis**: Fast processing with GPU acceleration support

### 📊 Comprehensive Results
- **Sentiment Classification**: Positive, Negative, or Neutral with confidence scores
- **Probability Distribution**: Shows confidence breakdown across all sentiment classes
- **Smart Summarization**: Automatically generates concise summaries of input text
- **Detailed Explanations**: AI-powered explanations for sentiment classifications
- **Key Phrase Extraction**: Identifies important words and phrases that influenced the decision

### 🎨 Modern Web Interface
- **Responsive Design**: Works perfectly on desktop, tablet, and mobile devices
- **Interactive UI**: Smooth animations and real-time feedback
- **Professional Styling**: Clean, modern design optimized for business use
- **Accessibility**: Keyboard shortcuts and screen reader support

## 📋 Prerequisites

Before running the application, ensure you have:

1. **Python 3.8 or higher**
2. **Fine-tuned FinBERT model** (from the training pipeline)
3. **Required Python packages** (see requirements.txt)
4. **GPU (optional)** for faster inference

## 🛠️ Installation

### 1. Clone or Navigate to Deployment Directory
```bash
cd deployment
```

### 2. Install Dependencies
```bash
pip install -r requirements.txt
```

### 3. Verify Model Path
Ensure your fine-tuned FinBERT model is available at:
```
../models/final/finbert_pretrained/
```

Or update the `MODEL_PATH` in `app.py` to point to your model location.

### 4. Download NLTK Data (if needed)
The application will automatically download required NLTK data, but you can manually ensure it's available:
```python
import nltk
nltk.download('punkt')
nltk.download('stopwords')
nltk.download('vader_lexicon')
```

## 🚀 Running the Application

### Development Mode
```bash
python app.py
```

The application will start on `http://localhost:5000`

### Production Mode
```bash
gunicorn -w 4 -b 0.0.0.0:5000 app:app
```

## 🧪 Testing

### Run API Tests
```bash
python test_api.py
```

This will run comprehensive tests including:
- Health check verification
- Model info validation
- Sentiment analysis with various text types
- Edge case handling
- Performance benchmarks

### Manual Testing
1. Open `http://localhost:5000` in your browser
2. Enter financial text in the text area
3. Click "Analyze Sentiment"
4. Review the comprehensive results

## 📖 API Documentation

### Endpoints

#### `GET /`
- **Description**: Main web interface
- **Returns**: HTML page with sentiment analysis form

#### `POST /analyze`
- **Description**: Analyze sentiment of provided text
- **Content-Type**: `application/json`
- **Request Body**:
  ```json
  {
    "text": "Your financial text here..."
  }
  ```
- **Response**:
  ```json
  {
    "text": "Cleaned input text",
    "sentiment": "positive|negative|neutral",
    "confidence": 0.8547,
    "summary": "Automatically generated summary",
    "explanation": "Why this sentiment was predicted",
    "key_phrases": ["key", "phrases", "found"],
    "all_probabilities": {
      "positive": 0.8547,
      "negative": 0.1203,
      "neutral": 0.0250
    },
    "analysis_timestamp": "2024-01-01T12:00:00",
    "text_length": 256,
    "word_count": 42
  }
  ```

#### `GET /health`
- **Description**: Health check endpoint
- **Returns**: API status and model availability

#### `GET /api/info`
- **Description**: Model information
- **Returns**: Model details and configuration

## 🎯 Usage Examples

### Example 1: Positive Financial News
**Input:**
```
Apple Inc. reported record quarterly earnings of $89.5 billion, surpassing analyst expectations by 12%. The company's iPhone sales grew 25% year-over-year, driven by strong demand for the new iPhone models.
```

**Expected Output:**
- **Sentiment**: Positive
- **Confidence**: ~85-95%
- **Key Phrases**: "record quarterly earnings", "surpassing expectations", "strong demand"

### Example 2: Negative Market News
**Input:**
```
Tesla shares plummeted 15% in after-hours trading following disappointing quarterly results. The electric vehicle manufacturer missed revenue expectations by $2.3 billion.
```

**Expected Output:**
- **Sentiment**: Negative
- **Confidence**: ~80-90%
- **Key Phrases**: "plummeted", "disappointing results", "missed expectations"

### Example 3: Neutral Financial Update
**Input:**
```
Microsoft Corporation will release its quarterly earnings report next Tuesday. Analysts forecast revenue of approximately $52.7 billion for the quarter.
```

**Expected Output:**
- **Sentiment**: Neutral
- **Confidence**: ~70-85%
- **Key Phrases**: "earnings report", "forecast", "approximately"

## 🔧 Configuration

### Model Configuration
Update `app.py` to modify:
- **MODEL_PATH**: Path to your fine-tuned model
- **MAX_LENGTH**: Maximum sequence length for tokenization
- **DEVICE**: Force CPU or GPU usage

### Web Configuration
- **HOST**: Change bind address (default: 0.0.0.0)
- **PORT**: Change port number (default: 5000)
- **DEBUG**: Enable/disable debug mode

## 🚨 Troubleshooting

### Common Issues

#### 1. Model Not Found Error
```
Error loading model: [Errno 2] No such file or directory
```
**Solution**: Verify the MODEL_PATH in app.py points to your fine-tuned model directory.

#### 2. CUDA Out of Memory
```
RuntimeError: CUDA out of memory
```
**Solution**: 
- Reduce batch size or use CPU inference
- Update device setting in app.py: `device = torch.device("cpu")`

#### 3. NLTK Data Missing
```
LookupError: Resource punkt not found
```
**Solution**: Run the NLTK downloads manually:
```python
import nltk
nltk.download('punkt')
nltk.download('stopwords')
```

#### 4. Port Already in Use
```
OSError: [Errno 98] Address already in use
```
**Solution**: Change port in app.py or kill the process using the port:
```bash
lsof -ti:5000 | xargs kill -9  # Linux/Mac
netstat -ano | findstr :5000   # Windows
```

## 📈 Performance

### Expected Performance
- **Analysis Time**: ~0.5-2 seconds per request
- **Accuracy**: 85-95% on financial text
- **Throughput**: 10-50 requests/minute (depending on hardware)

### Optimization Tips
1. **Use GPU**: Significantly faster inference
2. **Batch Processing**: For multiple texts, modify the API to accept arrays
3. **Model Quantization**: Reduce model size for deployment
4. **Caching**: Implement Redis for frequently analyzed texts

## 🔒 Security Considerations

- **Input Validation**: All inputs are sanitized and validated
- **Rate Limiting**: Consider adding rate limiting for production
- **HTTPS**: Use HTTPS in production environments
- **Authentication**: Add authentication for sensitive deployments

## 📄 License

This application is part of the FinBERT training project. Please refer to the main project license.

## 🤝 Contributing

1. Fork the project
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📞 Support

For issues and questions:
1. Check the troubleshooting section
2. Run the test script to validate setup
3. Review the logs for detailed error messages
4. Check model compatibility and paths

---

## 🎉 Quick Start

1. **Install dependencies**: `pip install -r requirements.txt`
2. **Verify model path**: Check `MODEL_PATH` in `app.py`
3. **Run the app**: `python app.py`
4. **Open browser**: Navigate to `http://localhost:5000`
5. **Test analysis**: Enter financial text and click "Analyze Sentiment"

Your FinBERT sentiment analysis web application is now ready! 🚀

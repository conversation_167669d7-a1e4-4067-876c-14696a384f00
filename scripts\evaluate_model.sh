#!/bin/bash

# FinBERT Evaluation Script
# Usage: ./scripts/evaluate_model.sh [model_path] [test_data] [text_column] [label_column]

set -e  # Exit on any error

# Default parameters
MODEL_PATH=${1:-"models/final/finbert_sentiment_final"}
TEST_DATA=${2:-"data/raw/data.csv"}
TEXT_COLUMN=${3:-"cleaned_text"}
LABEL_COLUMN=${4:-"labels"}
OUTPUT_DIR=${5:-"results"}

echo "========================================="
echo "FinBERT Model Evaluation"
echo "========================================="
echo "Model path: $MODEL_PATH"
echo "Test data: $TEST_DATA"
echo "Text column: $TEXT_COLUMN"
echo "Label column: $LABEL_COLUMN"
echo "Output directory: $OUTPUT_DIR"
echo "========================================="

# Check if model exists
if [ ! -d "$MODEL_PATH" ]; then
    echo "Error: Model directory '$MODEL_PATH' not found!"
    echo "Available models:"
    ls -la models/final/ 2>/dev/null || echo "No models found in models/final/"
    exit 1
fi

# Check if test data exists
if [ ! -f "$TEST_DATA" ]; then
    echo "Error: Test data file '$TEST_DATA' not found!"
    exit 1
fi

# Create necessary directories
mkdir -p results/metrics
mkdir -p results/plots
mkdir -p logs

# Activate virtual environment if it exists
if [ -d "venv" ]; then
    echo "Activating virtual environment..."
    source venv/bin/activate
fi

# Check if required packages are installed
python -c "import torch, transformers, pandas" 2>/dev/null || {
    echo "Error: Required packages not installed. Run: pip install -r requirements.txt"
    exit 1
}

# Run evaluation
echo "Starting model evaluation..."
echo "Timestamp: $(date)"

python src/evaluate.py \
    --model_path "$MODEL_PATH" \
    --test_data "$TEST_DATA" \
    --text_column "$TEXT_COLUMN" \
    --label_column "$LABEL_COLUMN" \
    --output_dir "$OUTPUT_DIR"

echo "========================================="
echo "Evaluation completed successfully!"
echo "Timestamp: $(date)"
echo "Check results/metrics/ for detailed metrics"
echo "Check results/plots/ for visualizations"
echo "Check results/ for prediction files"
echo "========================================="

# FinBERT Financial Sentiment Analysis

A comprehensive web application for financial sentiment analysis using a fine-tuned FinBERT model. This project provides real-time sentiment classification, article summarization, and detailed analysis justification for financial texts.

## 🌟 Features

- **Advanced Sentiment Analysis**: Classifies financial text as Positive (Bullish), Negative (Bearish), or Neutral
- **Article Summarization**: Extracts key points from longer financial texts
- **Analysis Justification**: Provides detailed explanations for sentiment predictions
- **Confidence Scores**: Shows probability distribution across all sentiment classes
- **Web Interface**: Modern, responsive design with interactive examples
- **REST API**: Programmatic access for integration with other systems
- **Real-time Processing**: Fast inference with GPU acceleration support

## 🚀 Demo

![FinBERT Demo](demo-screenshot.png)

## 📊 Model Performance

- **Accuracy**: 83.8% on financial sentiment classification
- **F1-Score**: 82.4% macro average
- **Training Data**: 5,844 financial sentences
- **Base Model**: ProsusAI/finbert (fine-tuned)

## 📁 Project Structure

```
finbert-sentiment-training/
├── src/                          # Source code
│   ├── config/                   # Configuration files
│   │   ├── __init__.py
│   │   └── training_config.py    # Training hyperparameters
│   ├── data/                     # Data processing modules
│   │   ├── __init__.py
│   │   ├── preprocessing.py      # Text cleaning and preprocessing
│   │   └── dataloader.py         # Dataset and DataLoader creation
│   ├── model/                    # Model-related code
│   │   ├── __init__.py
│   │   ├── config.py             # Model configuration
│   │   └── finbert_classifier.py # FinBERT classifier implementation
│   ├── utils/                    # Utility functions
│   │   ├── __init__.py
│   │   ├── metrics.py            # Metrics calculation
│   │   ├── logger.py             # Logging utilities
│   │   └── helpers.py            # Helper functions
│   ├── train.py                  # Main training script
│   └── evaluate.py               # Model evaluation script
├── notebooks/                    # Jupyter notebooks
│   ├── Data_Preprocessing_for_FinBERT.ipynb
│   ├── NLP.ipynb
│   ├── NLP2.ipynb
│   ├── Step2_Prepare_Dataset.ipynb
│   ├── Step3_Data_Preprocessing.ipynb
│   ├── Step3_Data_Preprocessing_Original_Splits.ipynb
│   └── Step4_Model_Training_FinBERT.ipynb
├── data/                         # Data storage
│   ├── raw/                      # Original datasets
│   │   └── data.csv
│   └── processed/                # Preprocessed datasets
│       ├── exported_csv_data/
│       ├── exported_csv_data_original_splits/
│       ├── preprocessed_datasets/
│       └── preprocessed_datasets_original_splits/
├── models/                       # Model storage
│   ├── checkpoints/              # Training checkpoints
│   └── final/                    # Final trained models
├── results/                      # Training and evaluation results
│   ├── metrics/                  # Metrics files
│   ├── plots/                    # Visualization plots
│   └── finbert_results/          # FinBERT specific results
├── deployment/                   # Deployment files
│   ├── app.py                    # Flask application
│   ├── test_api.py               # API testing script
│   ├── static/                   # Static web files
│   └── templates/                # HTML templates
├── scripts/                      # Shell scripts
├── logs/                         # Log files
├── requirements.txt              # Python dependencies
├── setup.py                      # Package setup
└── README.md                     # This file
```

## 🚀 Quick Start

### 1. Installation

```bash
# Clone the repository
git clone https://github.com/yourusername/finbert-sentiment-training.git
cd finbert-sentiment-training

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Or install in development mode
pip install -e .
```

### 2. Download Pre-trained Model

**⚠️ Important**: The fine-tuned model files are too large for GitHub (417MB). You need to download them separately:

```bash
# Option A: Automatic download (recommended)
python download_model.py

# Option B: Manual download
# 1. Download from: [LINK TO BE PROVIDED]
# 2. Extract to: models/final/finbert_pretrained/
# 3. Ensure these files exist:
#    - model.safetensors (417MB)
#    - config.json
#    - tokenizer.json
```

### 3. Data Preparation (For Training Your Own Model)

Place your dataset in the `data/raw/` directory. The CSV should contain:
- Text column (financial news/articles)
- Label column (0=Negative, 1=Neutral, 2=Positive)

### 3. Training

```bash
# Basic training
python src/train.py --data_file data/raw/your_data.csv --text_column "text" --label_column "labels"

# With custom output directory
python src/train.py --data_file data/raw/your_data.csv --output_dir models/custom

# For testing with sample data
python src/train.py --data_file data/raw/your_data.csv --sample_size 1000
```

### 4. Evaluation

```bash
# Evaluate trained model
python src/evaluate.py --model_path models/final/your_model --test_data data/raw/test_data.csv
```

### 5. Deployment

```bash
# Start Flask API
cd deployment
python app.py
```

## ⚙️ Configuration

### Training Configuration

Edit `src/config/training_config.py` to customize:

```python
@dataclass
class TrainingConfig:
    # Model settings
    model_name: str = "ProsusAI/finbert"
    num_labels: int = 3
    max_length: int = 512
    
    # Training hyperparameters
    learning_rate: float = 2e-5
    batch_size: int = 16
    num_epochs: int = 3
    
    # ... other parameters
```

### Custom Configuration

Create a JSON config file:

```json
{
    "learning_rate": 1e-5,
    "batch_size": 32,
    "num_epochs": 5,
    "max_length": 256
}
```

Use with: `python src/train.py --config_file my_config.json`

## 📊 Features

### Data Processing
- **Advanced text cleaning** preserving financial terminology
- **Tokenization** optimized for FinBERT
- **Stratified splitting** maintaining label distribution
- **Comprehensive data analysis** and visualization

### Model Training
- **FinBERT** pre-trained on financial texts
- **Hugging Face Trainer** integration
- **Early stopping** to prevent overfitting
- **Comprehensive metrics** tracking
- **Mixed precision** training support

### Evaluation & Metrics
- **Accuracy, Precision, Recall, F1-score**
- **Per-class metrics**
- **Confusion matrices**
- **Prediction confidence analysis**
- **Visualization** of results

### Deployment
- **Flask web API** for inference
- **Model serving** with confidence scores
- **Web interface** for testing
- **Batch prediction** support

## 📈 Model Performance

The trained FinBERT model achieves:
- **Accuracy**: ~85-90% on financial sentiment classification
- **F1-Score**: ~83-88% macro average
- **Inference time**: ~100-200ms per text

## 🛠️ Development

### Project Structure Principles
- **Modular design** for easy maintenance
- **Clear separation** of concerns
- **Comprehensive logging** and error handling
- **Type hints** and documentation
- **Unit tests** and validation

### Adding New Features

1. **Data preprocessing**: Add functions to `src/data/preprocessing.py`
2. **Model architectures**: Extend `src/model/finbert_classifier.py`
3. **Metrics**: Add custom metrics to `src/utils/metrics.py`
4. **Utilities**: Add helpers to `src/utils/helpers.py`

### Testing

```bash
# Run training with sample data
python src/train.py --data_file data/raw/data.csv --sample_size 100

# Test evaluation
python src/evaluate.py --model_path models/final/test_model --test_data data/raw/data.csv
```

## 📋 Requirements

- Python 3.8+
- PyTorch 2.0+
- Transformers 4.30+
- 8GB+ GPU memory (recommended)
- 16GB+ system RAM

## 🎯 Use Cases

- **Financial news sentiment analysis**
- **Stock market sentiment tracking**
- **Investment research automation**
- **Risk assessment** from text data
- **Market sentiment indicators**

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add your improvements
4. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- **Hugging Face** for the Transformers library
- **ProsusAI** for the FinBERT model
- **Financial sentiment analysis** research community

## 📞 Support

For questions and support:
- Open an issue on GitHub
- Check the documentation in `notebooks/`
- Review training logs in `logs/`

---

**Happy training!** 🚀📈

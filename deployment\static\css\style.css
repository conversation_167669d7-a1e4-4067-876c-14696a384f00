/* FinBERT Sentiment Analyzer Custom Styles */

:root {
    --primary-color: #007bff;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --light-gray: #f8f9fa;
    --dark-gray: #6c757d;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f7fa;
    line-height: 1.6;
}

/* Navigation Styles */
.navbar-brand {
    font-weight: 600;
    font-size: 1.4rem;
}

/* Card Enhancements */
.card {
    border-radius: 15px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
}

.card-header {
    border-radius: 15px 15px 0 0 !important;
    font-weight: 600;
}

/* Form Enhancements */
.form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

textarea.form-control {
    resize: vertical;
    min-height: 200px;
}

/* Button Enhancements */
.btn {
    border-radius: 25px;
    font-weight: 500;
    padding: 10px 25px;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(45deg, #007bff, #0056b3);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(45deg, #0056b3, #004085);
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
}

.btn-lg {
    padding: 12px 30px;
    font-size: 1.1rem;
}

/* Sentiment Badge Styles */
.sentiment-positive {
    background: linear-gradient(45deg, #28a745, #20c997);
    color: white;
    padding: 10px 20px;
    border-radius: 25px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.sentiment-negative {
    background: linear-gradient(45deg, #dc3545, #c82333);
    color: white;
    padding: 10px 20px;
    border-radius: 25px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.sentiment-neutral {
    background: linear-gradient(45deg, #6c757d, #5a6268);
    color: white;
    padding: 10px 20px;
    border-radius: 25px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Probability Bars */
.probability-bar {
    margin-bottom: 8px;
}

.probability-label {
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 3px;
    text-transform: capitalize;
}

.progress {
    height: 8px;
    border-radius: 10px;
    background-color: #e9ecef;
}

.progress-bar {
    border-radius: 10px;
    transition: width 0.8s ease;
}

.progress-bar.bg-success {
    background: linear-gradient(90deg, #28a745, #20c997) !important;
}

.progress-bar.bg-danger {
    background: linear-gradient(90deg, #dc3545, #c82333) !important;
}

.progress-bar.bg-secondary {
    background: linear-gradient(90deg, #6c757d, #5a6268) !important;
}

/* Key Phrases */
.key-phrase {
    display: inline-block;
    background: linear-gradient(45deg, #e3f2fd, #bbdefb);
    color: #1976d2;
    padding: 5px 12px;
    margin: 3px;
    border-radius: 15px;
    font-size: 0.9rem;
    font-weight: 500;
    border: 1px solid #90caf9;
    transition: all 0.3s ease;
}

.key-phrase:hover {
    background: linear-gradient(45deg, #bbdefb, #90caf9);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(25, 118, 210, 0.3);
}

/* Summary and Explanation Cards */
.summary-card, .explanation-card {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border: 1px solid #dee2e6;
    border-radius: 12px;
    padding: 20px;
    margin: 10px 0;
}

/* Loading Animation */
.spinner-border {
    width: 3rem;
    height: 3rem;
}

/* Feature Cards */
.feature-card {
    transition: all 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.feature-icon {
    transition: transform 0.3s ease;
}

.feature-card:hover .feature-icon {
    transform: scale(1.1);
}

/* Alert Enhancements */
.alert {
    border-radius: 12px;
    border: none;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da, #f5c6cb);
    color: #721c24;
}

/* Responsive Design */
@media (max-width: 768px) {
    .display-4 {
        font-size: 2rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .btn-lg {
        padding: 10px 20px;
        font-size: 1rem;
    }
    
    .sentiment-positive,
    .sentiment-negative,
    .sentiment-neutral {
        padding: 8px 15px;
        font-size: 0.9rem;
    }
}

/* Animation Classes */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.pulse-animation {
    animation: pulse 2s infinite;
}

/* Text Styling */
.text-gradient {
    background: linear-gradient(45deg, #007bff, #28a745);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
}

/* Footer */
footer {
    margin-top: auto;
}

/* Confidence Score Styling */
.confidence-score {
    font-size: 1.1rem;
    font-weight: 600;
    padding: 5px 15px;
    border-radius: 20px;
    background: linear-gradient(45deg, #17a2b8, #138496);
    color: white;
    display: inline-block;
}

/* Section Headers */
.section-header {
    border-bottom: 3px solid var(--primary-color);
    padding-bottom: 10px;
    margin-bottom: 20px;
    font-weight: 600;
    color: var(--primary-color);
}

/* Interactive Elements */
.interactive-element {
    cursor: pointer;
    transition: all 0.3s ease;
}

.interactive-element:hover {
    opacity: 0.8;
    transform: scale(1.02);
}

/* Scroll Animations */
.scroll-animation {
    opacity: 0;
    transform: translateY(50px);
    transition: all 0.6s ease;
}

.scroll-animation.visible {
    opacity: 1;
    transform: translateY(0);
}

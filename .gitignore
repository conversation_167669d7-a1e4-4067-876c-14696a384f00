# FinBERT Sentiment Analysis - Git Ignore

# Large model files (too big for GitHub)
models/
*.bin
*.safetensors
*.pt
*.pth
*.ckpt

# Large datasets
data/raw/
data/processed/
*.csv
*.json
*.parquet

# Jupyter notebook outputs and checkpoints
.ipynb_checkpoints/
*/.ipynb_checkpoints/*

# Python cache and compiled files
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
venv/
env/
ENV/
.venv/

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs and results (can be large)
logs/
results/
*.log

# Temporary files
*.tmp
*.temp
temp/
tmp/

# Environment variables
.env
.env.local
.env.*.local

# Flask instance folder
instance/

# Coverage reports
htmlcov/
.coverage
.coverage.*
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Documentation builds
docs/_build/

# PyBuilder
target/

# Celery
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

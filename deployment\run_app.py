#!/usr/bin/env python3
"""
FinBERT Sentiment Analysis Application Launcher
Automated setup and launch script for the Flask web application
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        print(f"Current version: {sys.version}")
        return False
    print(f"✅ Python version: {sys.version.split()[0]}")
    return True

def check_dependencies():
    """Check if required packages are installed."""
    required_packages = [
        'flask', 'torch', 'transformers', 'nltk', 'numpy', 'pandas', 'sklearn'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} is installed")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} is missing")
    
    if missing_packages:
        print(f"\n📦 Installing missing packages: {', '.join(missing_packages)}")
        try:
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
            ])
            print("✅ Dependencies installed successfully")
            return True
        except subprocess.CalledProcessError:
            print("❌ Failed to install dependencies")
            return False
    
    return True

def find_model():
    """Find the trained FinBERT model."""
    model_paths = [
        "./finbert-sentiment-final",
        "../models/final/finbert_pretrained",
        "../models/checkpoints/checkpoint-285",
        "../models/checkpoints/checkpoint-190",
        "./models/final/finbert_pretrained",
        "./models/checkpoints/checkpoint-285",
        "./models/checkpoints/checkpoint-190"
    ]
    
    for path in model_paths:
        if os.path.exists(path):
            config_file = os.path.join(path, "config.json")
            if os.path.exists(config_file):
                print(f"✅ Found model at: {path}")
                return path
    
    print("❌ No trained model found!")
    print("Please ensure you have trained the FinBERT model first.")
    print("Run the training notebook: notebooks/FinBERT_Complete_Training_Colab.ipynb")
    return None

def download_nltk_data():
    """Download required NLTK data."""
    print("📚 Downloading NLTK data...")
    try:
        import nltk
        nltk.download('punkt', quiet=True)
        nltk.download('stopwords', quiet=True)
        nltk.download('vader_lexicon', quiet=True)
        print("✅ NLTK data downloaded")
        return True
    except Exception as e:
        print(f"⚠️  NLTK data download failed: {e}")
        return False

def get_available_port(start_port=5000):
    """Find an available port starting from the given port."""
    import socket
    
    for port in range(start_port, start_port + 100):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                sock.bind(('localhost', port))
                return port
        except OSError:
            continue
    return start_port  # Fallback to original port

def launch_app(port=5000, debug=True):
    """Launch the Flask application."""
    print(f"\n🚀 Starting FinBERT Sentiment Analysis Web App...")
    print(f"🌐 URL: http://localhost:{port}")
    print(f"📱 Access from mobile/other devices: http://[your-ip]:{port}")
    print("\n⌨️  Keyboard shortcuts:")
    print("   - Ctrl+C: Stop the server")
    print("   - Ctrl+K: Focus text input (in browser)")
    print("   - Ctrl+Enter: Analyze text (in browser)")
    print("   - Escape: Clear form (in browser)")
    
    try:
        # Import and run the Flask app
        import app as flask_app
        flask_app.app.run(
            host='0.0.0.0',
            port=port,
            debug=debug,
            threaded=True
        )
    except KeyboardInterrupt:
        print("\n👋 Application stopped by user")
    except Exception as e:
        print(f"\n❌ Error starting application: {e}")
        print("\nTrying alternative startup method...")
        try:
            # Alternative method - run directly
            import subprocess
            import sys
            subprocess.run([sys.executable, "app.py"], check=True)
        except Exception as e2:
            print(f"❌ Alternative method also failed: {e2}")

def main():
    """Main launcher function."""
    print("=" * 60)
    print("🧠 FinBERT Sentiment Analysis Web Application")
    print("=" * 60)
    print()
    
    # Change to deployment directory
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    # Check system requirements
    if not check_python_version():
        sys.exit(1)
    
    # Check and install dependencies
    if not check_dependencies():
        print("\n❌ Dependency installation failed")
        print("Please run: pip install -r requirements.txt")
        sys.exit(1)
    
    # Find the trained model
    model_path = find_model()
    if not model_path:
        print("\n🔍 Model not found. Please check the following:")
        print("1. Have you trained the FinBERT model?")
        print("2. Is the model saved in the correct location?")
        print("3. Run the training notebook first if needed.")
        
        proceed = input("\nDo you want to continue anyway? (y/N): ").lower()
        if proceed != 'y':
            sys.exit(1)
    
    # Download NLTK data
    download_nltk_data()
    
    # Get port
    port = get_available_port(5000)
    if port != 5000:
        print(f"⚠️  Port 5000 is busy, using port {port} instead")
    
    # Launch application
    try:
        launch_app(port=port, debug=True)
    except Exception as e:
        print(f"\n❌ Failed to start application: {e}")
        print("\nTroubleshooting tips:")
        print("1. Check if another application is using the port")
        print("2. Verify all dependencies are installed")
        print("3. Ensure the model files are accessible")
        print("4. Check the console output for detailed error messages")

if __name__ == "__main__":
    main()

"""
Data preprocessing module for FinBERT sentiment analysis.
Contains functions for cleaning and preparing financial text data.
"""

import re
import pandas as pd
import numpy as np
from typing import List, Dict, Optional, Tuple
import logging
from collections import Counter

logger = logging.getLogger(__name__)

def clean_financial_text(text: str) -> str:
    """
    Clean financial text while preserving important financial information.
    
    Args:
        text: Raw text to clean
        
    Returns:
        Cleaned text
    """
    if not isinstance(text, str) or pd.isna(text):
        return ""
    
    # Preserve financial symbols temporarily
    text = re.sub(r'\$', ' DOLLARSYMBOL ', text)
    text = re.sub(r'%', ' PERCENTSYMBOL ', text)
    text = re.sub(r'€', ' EUROSYMBOL ', text)
    text = re.sub(r'£', ' POUNDSYMBOL ', text)
    
    # Standardize number formats
    text = re.sub(r'(\d),(\d)', r'\1\2', text)
    
    # Normalize date formats
    text = re.sub(r'(\d+)/(\d+)/(\d+)', r'\1-\2-\3', text)
    
    # Remove HTML tags
    text = re.sub(r'<.*?>', ' ', text)
    
    # Handle URLs and emails
    text = re.sub(r'https?://\S+|www\.\S+', ' URLTOKEN ', text)
    text = re.sub(r'\S+@\S+', ' EMAILTOKEN ', text)
    
    # Standardize financial abbreviations
    financial_abbr = {
        r'\bQ(\d)\b': r'quarter \1',
        r'\bFY(\d{2,4})\b': r'fiscal year \1',
        r'\bEPS\b': r'earnings per share',
        r'\bP/E\b': r'price to earnings',
        r'\bROI\b': r'return on investment',
        r'\bYOY\b': r'year over year',
        r'\bQOQ\b': r'quarter over quarter'
    }
    
    for pattern, replacement in financial_abbr.items():
        text = re.sub(pattern, replacement, text)
    
    # Clean special characters while preserving important punctuation
    text = re.sub(r'[^\w\s.,!?:;()\'"-]', ' ', text)
    
    # Restore financial symbols
    text = text.replace(' DOLLARSYMBOL ', ' $ ')
    text = text.replace(' PERCENTSYMBOL ', ' % ')
    text = text.replace(' EUROSYMBOL ', ' € ')
    text = text.replace(' POUNDSYMBOL ', ' £ ')
    
    # Normalize whitespace
    text = re.sub(r'\s+', ' ', text).strip()
    
    return text

def preprocess_dataframe(df: pd.DataFrame, 
                        text_column: str,
                        label_column: str = 'labels',
                        min_text_length: int = 5) -> pd.DataFrame:
    """
    Preprocess a DataFrame for FinBERT training.
    
    Args:
        df: Input DataFrame
        text_column: Name of the text column
        label_column: Name of the label column
        min_text_length: Minimum text length in words
        
    Returns:
        Cleaned DataFrame
    """
    logger.info(f"Preprocessing DataFrame with {len(df)} rows")
    
    # Create a copy to avoid modifying original
    df_clean = df.copy()
    
    # Drop rows with null values in essential columns
    essential_columns = [text_column, label_column]
    initial_size = len(df_clean)
    df_clean = df_clean.dropna(subset=essential_columns)
    logger.info(f"Removed {initial_size - len(df_clean)} rows with null values")
    
    # Clean text
    df_clean['cleaned_text'] = df_clean[text_column].apply(clean_financial_text)
    
    # Calculate text length
    df_clean['text_length'] = df_clean['cleaned_text'].apply(lambda x: len(x.split()))
    
    # Filter out very short texts
    before_filter = len(df_clean)
    df_clean = df_clean[df_clean['text_length'] >= min_text_length]
    logger.info(f"Removed {before_filter - len(df_clean)} rows with text < {min_text_length} words")
    
    # Reset index
    df_clean = df_clean.reset_index(drop=True)
    
    logger.info(f"Preprocessing complete. Final size: {len(df_clean)} rows")
    
    return df_clean

def analyze_label_distribution(df: pd.DataFrame, label_column: str = 'labels') -> Dict:
    """
    Analyze the distribution of labels in the dataset.
    
    Args:
        df: DataFrame to analyze
        label_column: Name of the label column
        
    Returns:
        Dictionary with distribution statistics
    """
    label_counts = df[label_column].value_counts()
    total_samples = len(df)
    
    distribution = {
        'total_samples': total_samples,
        'label_counts': label_counts.to_dict(),
        'label_percentages': (label_counts / total_samples * 100).to_dict(),
        'is_balanced': (label_counts.max() / label_counts.min()) < 2.0 if len(label_counts) > 1 else True
    }
    
    return distribution

def get_text_statistics(df: pd.DataFrame, text_column: str = 'cleaned_text') -> Dict:
    """
    Get statistics about text lengths in the dataset.
    
    Args:
        df: DataFrame to analyze
        text_column: Name of the text column
        
    Returns:
        Dictionary with text statistics
    """
    text_lengths = df[text_column].apply(lambda x: len(x.split()) if isinstance(x, str) else 0)
    
    statistics = {
        'mean_length': text_lengths.mean(),
        'median_length': text_lengths.median(),
        'std_length': text_lengths.std(),
        'min_length': text_lengths.min(),
        'max_length': text_lengths.max(),
        'percentiles': {
            '25%': text_lengths.quantile(0.25),
            '75%': text_lengths.quantile(0.75),
            '90%': text_lengths.quantile(0.90),
            '95%': text_lengths.quantile(0.95),
            '99%': text_lengths.quantile(0.99)
        }
    }
    
    return statistics

def get_top_words_by_sentiment(df: pd.DataFrame, 
                              text_column: str = 'cleaned_text',
                              label_column: str = 'labels',
                              n_words: int = 20) -> Dict:
    """
    Get top words for each sentiment class.
    
    Args:
        df: DataFrame to analyze
        text_column: Name of the text column
        label_column: Name of the label column
        n_words: Number of top words to return
        
    Returns:
        Dictionary with top words for each sentiment
    """
    sentiment_words = {}
    
    # Common stopwords to exclude
    stop_words = {
        'the', 'and', 'to', 'of', 'a', 'in', 'for', 'is', 'on', 'that', 'by', 
        'this', 'with', 'i', 'you', 'it', 'not', 'or', 'be', 'are', 'from', 
        'at', 'as', 'your', 'have', 'was', 'will', 'would', 'could', 'should'
    }
    
    for label in df[label_column].unique():
        # Get texts for this label
        label_texts = df[df[label_column] == label][text_column].dropna()
        
        # Combine all texts
        all_text = ' '.join(label_texts.tolist()).lower()
        
        # Clean and split into words
        all_text = re.sub(r'[^\w\s]', '', all_text)
        words = all_text.split()
        
        # Filter out stopwords
        words = [word for word in words if word not in stop_words and len(word) > 2]
        
        # Count words
        word_counts = Counter(words)
        top_words = word_counts.most_common(n_words)
        
        sentiment_words[label] = top_words
    
    return sentiment_words

def split_data(df: pd.DataFrame, 
              train_ratio: float = 0.8,
              val_ratio: float = 0.1,
              random_state: int = 42) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]:
    """
    Split data into train, validation, and test sets.
    
    Args:
        df: DataFrame to split
        train_ratio: Proportion for training set
        val_ratio: Proportion for validation set
        random_state: Random seed for reproducibility
        
    Returns:
        Tuple of (train_df, val_df, test_df)
    """
    # Stratified split to maintain label distribution
    from sklearn.model_selection import train_test_split
    
    # First split: train + val vs test
    train_val_df, test_df = train_test_split(
        df, 
        test_size=1 - train_ratio - val_ratio,
        stratify=df['labels'],
        random_state=random_state
    )
    
    # Second split: train vs val
    if val_ratio > 0:
        val_size = val_ratio / (train_ratio + val_ratio)
        train_df, val_df = train_test_split(
            train_val_df,
            test_size=val_size,
            stratify=train_val_df['labels'],
            random_state=random_state
        )
    else:
        train_df = train_val_df
        val_df = pd.DataFrame()
    
    return train_df, val_df, test_df

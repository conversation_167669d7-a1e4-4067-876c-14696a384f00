"""
FinBERT Classifier for financial sentiment analysis.
"""

import torch
import torch.nn as nn
from transformers import AutoModelForSequenceClassification, AutoTokenizer
from typing import Dict, List, Optional, Tuple
import numpy as np
import logging

logger = logging.getLogger(__name__)

class FinBERTClassifier:
    """
    FinBERT-based sentiment classifier for financial texts.
    """
    
    def __init__(self, 
                 model_name: str = "ProsusAI/finbert",
                 num_labels: int = 3,
                 device: Optional[str] = None):
        """
        Initialize FinBERT classifier.
        
        Args:
            model_name: Pretrained model name
            num_labels: Number of classification labels
            device: Device to run model on
        """
        self.model_name = model_name
        self.num_labels = num_labels
        self.device = device or ("cuda" if torch.cuda.is_available() else "cpu")
        
        self.model = None
        self.tokenizer = None
        self.label_mapping = {0: "Negative", 1: "Neutral", 2: "Positive"}
        
        self._load_model()
    
    def _load_model(self):
        """Load the pretrained FinBERT model and tokenizer."""
        try:
            logger.info(f"Loading FinBERT model: {self.model_name}")
            
            # Load tokenizer
            self.tokenizer = AutoTokenizer.from_pretrained(self.model_name)
            
            # Load model
            self.model = AutoModelForSequenceClassification.from_pretrained(
                self.model_name,
                num_labels=self.num_labels,
                problem_type="single_label_classification"
            )
            
            # Move to device
            self.model = self.model.to(self.device)
            logger.info(f"Model loaded successfully on {self.device}")
            
        except Exception as e:
            logger.error(f"Error loading model: {e}")
            raise
    
    def predict(self, 
                texts: List[str], 
                return_probabilities: bool = False,
                max_length: int = 512) -> Dict:
        """
        Predict sentiment for given texts.
        
        Args:
            texts: List of texts to classify
            return_probabilities: Whether to return class probabilities
            max_length: Maximum sequence length
            
        Returns:
            Dictionary with predictions and optionally probabilities
        """
        if self.model is None:
            raise ValueError("Model not loaded. Call _load_model() first.")
        
        self.model.eval()
        
        predictions = []
        probabilities = []
        
        with torch.no_grad():
            for text in texts:
                # Tokenize
                inputs = self.tokenizer(
                    text,
                    padding=True,
                    truncation=True,
                    max_length=max_length,
                    return_tensors="pt"
                )
                
                # Move to device
                inputs = {k: v.to(self.device) for k, v in inputs.items()}
                
                # Forward pass
                outputs = self.model(**inputs)
                logits = outputs.logits
                
                # Get predictions
                predicted_class = torch.argmax(logits, dim=-1).cpu().numpy()[0]
                predictions.append(predicted_class)
                
                # Get probabilities if requested
                if return_probabilities:
                    probs = torch.nn.functional.softmax(logits, dim=-1).cpu().numpy()[0]
                    probabilities.append(probs)
        
        result = {
            "predictions": predictions,
            "predicted_labels": [self.label_mapping[pred] for pred in predictions]
        }
        
        if return_probabilities:
            result["probabilities"] = probabilities
        
        return result
    
    def predict_single(self, 
                      text: str, 
                      return_probabilities: bool = False,
                      max_length: int = 512) -> Dict:
        """
        Predict sentiment for a single text.
        
        Args:
            text: Text to classify
            return_probabilities: Whether to return class probabilities
            max_length: Maximum sequence length
            
        Returns:
            Dictionary with prediction and optionally probabilities
        """
        result = self.predict([text], return_probabilities, max_length)
        
        return {
            "prediction": result["predictions"][0],
            "predicted_label": result["predicted_labels"][0],
            "probabilities": result.get("probabilities", [None])[0]
        }
    
    def save_model(self, save_path: str, include_tokenizer: bool = True):
        """
        Save the model to specified path.
        
        Args:
            save_path: Path to save the model
            include_tokenizer: Whether to save tokenizer as well
        """
        if self.model is None:
            raise ValueError("No model to save")
        
        logger.info(f"Saving model to {save_path}")
        
        # Save model
        self.model.save_pretrained(save_path)
        
        # Save tokenizer
        if include_tokenizer:
            self.tokenizer.save_pretrained(save_path)
        
        # Save additional metadata
        metadata = {
            "model_name": self.model_name,
            "num_labels": self.num_labels,
            "label_mapping": self.label_mapping,
            "device": self.device
        }
        
        import json
        with open(f"{save_path}/model_metadata.json", "w") as f:
            json.dump(metadata, f, indent=2)
        
        logger.info("Model saved successfully")
    
    def load_model(self, model_path: str):
        """
        Load a previously saved model.
        
        Args:
            model_path: Path to the saved model
        """
        try:
            logger.info(f"Loading model from {model_path}")
            
            # Load model
            self.model = AutoModelForSequenceClassification.from_pretrained(model_path)
            self.model = self.model.to(self.device)
            
            # Load tokenizer
            self.tokenizer = AutoTokenizer.from_pretrained(model_path)
            
            # Load metadata if available
            try:
                import json
                with open(f"{model_path}/model_metadata.json", "r") as f:
                    metadata = json.load(f)
                    self.label_mapping = metadata.get("label_mapping", self.label_mapping)
                    self.num_labels = metadata.get("num_labels", self.num_labels)
            except FileNotFoundError:
                logger.warning("Model metadata not found, using defaults")
            
            logger.info("Model loaded successfully")
            
        except Exception as e:
            logger.error(f"Error loading model: {e}")
            raise

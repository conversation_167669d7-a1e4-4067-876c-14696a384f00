{"dataset_info": {"source_file": "data.csv", "original_samples": 799, "final_samples": 5321, "samples_removed": -4522}, "preprocessing_config": {"model_name": "ProsusAI/finbert", "max_length": 128, "train_ratio": 0.7, "val_ratio": 0.15, "test_ratio": 0.15, "random_seed": 42}, "split_sizes": {"train": 3724, "validation": 798, "test": 799}, "label_mapping": {"negative": 0, "neutral": 1, "positive": 2}, "reverse_label_mapping": {"0": "negative", "1": "neutral", "2": "positive"}, "validation_results": {"passed": true, "issues": [], "warnings": [], "summary": {"total_samples": 5321, "splits": {"train": 3724, "validation": 798, "test": 799}, "max_length": 128, "vocab_size": 30522, "label_mapping": {"negative": 0, "neutral": 1, "positive": 2}, "issues_count": 0, "warnings_count": 0}}, "processing_timestamp": "2025-07-21T17:53:36.059347"}
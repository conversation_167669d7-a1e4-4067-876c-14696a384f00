"""
Test script for FinBERT Sentiment Analysis API
Tests all endpoints and functionality
"""

import requests
import json
import time
from typing import Dict, Any

class APITester:
    def __init__(self, base_url: str = "http://localhost:5000"):
        self.base_url = base_url
        self.session = requests.Session()
    
    def test_health_endpoint(self) -> bool:
        """Test the health check endpoint."""
        try:
            print("🏥 Testing health endpoint...")
            response = self.session.get(f"{self.base_url}/health")
            
            if response.status_code == 200:
                health_data = response.json()
                print(f"✅ Health check passed: {health_data}")
                return health_data.get('status') == 'healthy'
            else:
                print(f"❌ Health check failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Health check error: {e}")
            return False
    
    def test_model_info_endpoint(self) -> bool:
        """Test the model info endpoint."""
        try:
            print("\n📊 Testing model info endpoint...")
            response = self.session.get(f"{self.base_url}/api/info")
            
            if response.status_code == 200:
                info_data = response.json()
                print(f"✅ Model info retrieved: {info_data}")
                return True
            else:
                print(f"❌ Model info failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Model info error: {e}")
            return False
    
    def test_sentiment_analysis(self, text: str, expected_sentiment: str = None) -> Dict[str, Any]:
        """Test sentiment analysis with given text."""
        try:
            print(f"\n🧠 Testing sentiment analysis...")
            print(f"Text: {text[:100]}...")
            
            start_time = time.time()
            
            response = self.session.post(
                f"{self.base_url}/analyze",
                json={"text": text},
                headers={"Content-Type": "application/json"}
            )
            
            analysis_time = time.time() - start_time
            
            if response.status_code == 200:
                result = response.json()
                
                print(f"✅ Analysis completed in {analysis_time:.2f} seconds")
                print(f"   Sentiment: {result['sentiment']}")
                print(f"   Confidence: {result['confidence']:.4f}")
                print(f"   Summary: {result['summary'][:100]}...")
                print(f"   Key phrases: {result.get('key_phrases', [])}")
                
                if expected_sentiment and result['sentiment'].lower() != expected_sentiment.lower():
                    print(f"⚠️  Expected {expected_sentiment}, got {result['sentiment']}")
                
                return result
            else:
                error_data = response.json() if response.content else {"error": "Unknown error"}
                print(f"❌ Analysis failed: {response.status_code} - {error_data}")
                return {"error": error_data}
                
        except Exception as e:
            print(f"❌ Analysis error: {e}")
            return {"error": str(e)}
    
    def test_edge_cases(self) -> None:
        """Test various edge cases."""
        print("\n🔍 Testing edge cases...")
        
        # Test empty text
        print("\n- Testing empty text:")
        empty_response = self.session.post(
            f"{self.base_url}/analyze",
            json={"text": ""},
            headers={"Content-Type": "application/json"}
        )
        if empty_response.status_code == 400:
            print("✅ Empty text handled correctly")
        else:
            print(f"❌ Empty text not handled: {empty_response.status_code}")
        
        # Test very short text
        print("\n- Testing very short text:")
        short_response = self.session.post(
            f"{self.base_url}/analyze",
            json={"text": "Hi"},
            headers={"Content-Type": "application/json"}
        )
        if short_response.status_code == 400:
            print("✅ Short text handled correctly")
        else:
            print(f"⚠️  Short text result: {short_response.status_code}")
        
        # Test very long text
        print("\n- Testing very long text:")
        long_text = "This is a test sentence. " * 100
        long_response = self.session.post(
            f"{self.base_url}/analyze",
            json={"text": long_text},
            headers={"Content-Type": "application/json"}
        )
        if long_response.status_code == 200:
            print("✅ Long text handled correctly")
        else:
            print(f"❌ Long text failed: {long_response.status_code}")
        
        # Test invalid JSON
        print("\n- Testing invalid request format:")
        invalid_response = self.session.post(
            f"{self.base_url}/analyze",
            json={"wrong_field": "test"},
            headers={"Content-Type": "application/json"}
        )
        if invalid_response.status_code == 400:
            print("✅ Invalid format handled correctly")
        else:
            print(f"❌ Invalid format not handled: {invalid_response.status_code}")
    
    def run_comprehensive_test(self) -> None:
        """Run all tests."""
        print("🚀 Starting comprehensive API testing...\n")
        
        # Test health
        health_ok = self.test_health_endpoint()
        
        # Test model info
        info_ok = self.test_model_info_endpoint()
        
        if not health_ok:
            print("\n❌ Health check failed - skipping other tests")
            return
        
        # Test cases with different sentiments
        test_cases = [
            {
                "text": "Apple reported record quarterly earnings, beating analyst expectations by 15%. Strong iPhone sales drove revenue growth of 25% year-over-year.",
                "expected": "positive"
            },
            {
                "text": "The company's stock price crashed 20% following disappointing earnings results. Revenue fell short of expectations due to declining sales and increased competition.",
                "expected": "negative"
            },
            {
                "text": "The Federal Reserve announced that interest rates will remain unchanged at the current level. The central bank cited stable economic conditions in their decision.",
                "expected": "neutral"
            },
            {
                "text": "Tesla's innovative electric vehicle technology continues to revolutionize the automotive industry. The company's sustainable energy solutions show tremendous growth potential.",
                "expected": "positive"
            },
            {
                "text": "Economic uncertainties and market volatility have raised concerns among investors. Several major companies have announced layoffs and cost-cutting measures.",
                "expected": "negative"
            }
        ]
        
        # Run sentiment analysis tests
        results = []
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n{'='*60}")
            print(f"Test Case {i}: Expected {test_case['expected']} sentiment")
            print(f"{'='*60}")
            
            result = self.test_sentiment_analysis(
                test_case["text"], 
                test_case["expected"]
            )
            results.append(result)
        
        # Test edge cases
        self.test_edge_cases()
        
        # Summary
        print(f"\n{'='*60}")
        print("📊 TEST SUMMARY")
        print(f"{'='*60}")
        
        successful_tests = sum(1 for r in results if "error" not in r)
        total_tests = len(results)
        
        print(f"Successful sentiment analyses: {successful_tests}/{total_tests}")
        print(f"Health check: {'✅ Pass' if health_ok else '❌ Fail'}")
        print(f"Model info: {'✅ Pass' if info_ok else '❌ Fail'}")
        
        if successful_tests == total_tests and health_ok and info_ok:
            print("\n🎉 All tests passed! API is working correctly.")
        else:
            print("\n⚠️  Some tests failed. Check the API setup.")

def main():
    """Main function to run the tests."""
    print("FinBERT Sentiment Analysis API Tester")
    print("=====================================\n")
    
    # You can change the URL if running on a different host/port
    api_url = input("Enter API URL (default: http://localhost:5000): ").strip()
    if not api_url:
        api_url = "http://localhost:5000"
    
    tester = APITester(api_url)
    
    try:
        tester.run_comprehensive_test()
    except KeyboardInterrupt:
        print("\n\n⏹️  Testing interrupted by user")
    except Exception as e:
        print(f"\n❌ Testing failed with error: {e}")

if __name__ == "__main__":
    main()

# FinBERT Sentiment Analysis Web Application Launcher (PowerShell)

Write-Host "================================================================" -ForegroundColor Cyan
Write-Host "🧠 FinBERT Sentiment Analysis Web Application" -ForegroundColor Green
Write-Host "================================================================" -ForegroundColor Cyan
Write-Host ""

# Change to script directory
Set-Location $PSScriptRoot

# Check Python installation
Write-Host "🔍 Checking Python installation..." -ForegroundColor Yellow
try {
    $pythonVersion = python --version 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Python found: $pythonVersion" -ForegroundColor Green
    } else {
        throw "Python not found"
    }
} catch {
    Write-Host "❌ Python is not installed or not in PATH" -ForegroundColor Red
    Write-Host "Please install Python 3.8+ and add it to your PATH" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

# Install dependencies
Write-Host ""
Write-Host "📦 Installing/checking dependencies..." -ForegroundColor Yellow
try {
    python -m pip install -r requirements.txt
    if ($LASTEXITCODE -ne 0) {
        throw "Failed to install dependencies"
    }
    Write-Host "✅ Dependencies installed successfully" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to install dependencies" -ForegroundColor Red
    Write-Host "Please run: pip install -r requirements.txt" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

# Check for model
Write-Host ""
Write-Host "🔍 Checking for trained model..." -ForegroundColor Yellow
$modelPaths = @(
    "./finbert-sentiment-final",
    "../models/final/finbert_pretrained",
    "../models/checkpoints/checkpoint-285",
    "../models/checkpoints/checkpoint-190"
)

$modelFound = $false
foreach ($path in $modelPaths) {
    if (Test-Path $path) {
        $configPath = Join-Path $path "config.json"
        if (Test-Path $configPath) {
            Write-Host "✅ Found model at: $path" -ForegroundColor Green
            $modelFound = $true
            break
        }
    }
}

if (-not $modelFound) {
    Write-Host "⚠️  No trained model found!" -ForegroundColor Yellow
    Write-Host "Please ensure you have trained the FinBERT model first." -ForegroundColor Yellow
    $continue = Read-Host "Do you want to continue anyway? (y/N)"
    if ($continue -ne "y" -and $continue -ne "Y") {
        exit 1
    }
}

# Start the application
Write-Host ""
Write-Host "🚀 Starting FinBERT Sentiment Analysis App..." -ForegroundColor Green
Write-Host "🌐 Open your browser and go to: http://localhost:5000" -ForegroundColor Cyan
Write-Host ""
Write-Host "⌨️  Controls:" -ForegroundColor Yellow
Write-Host "   - Press Ctrl+C to stop the server" -ForegroundColor Gray
Write-Host "   - In browser: Ctrl+K to focus input, Ctrl+Enter to analyze" -ForegroundColor Gray
Write-Host ""

try {
    python run_app.py
} catch {
    Write-Host "❌ Error starting application: $_" -ForegroundColor Red
    Write-Host ""
    Write-Host "Troubleshooting tips:" -ForegroundColor Yellow
    Write-Host "1. Check if port 5000 is already in use" -ForegroundColor Gray
    Write-Host "2. Verify all dependencies are installed" -ForegroundColor Gray
    Write-Host "3. Ensure model files are accessible" -ForegroundColor Gray
}

Read-Host "Press Enter to exit"

"""
FinBERT Financial Sentiment Analysis Web Application

A Flask web application that uses a fine-tuned FinBERT model to analyze
financial articles and provide sentiment analysis, summaries, and justifications.
"""

import os
import sys
import logging
from datetime import datetime
from typing import Dict, List, Tuple
import re

from flask import Flask, render_template, request, jsonify, flash
import torch
from transformers import AutoModelForSequenceClassification, AutoTokenizer
import numpy as np

# Add src to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
app.secret_key = 'finbert-sentiment-analysis-2024'

class FinBERTAnalyzer:
    """Enhanced FinBERT analyzer with summarization and justification capabilities."""

    def __init__(self, model_path: str):
        """Initialize the analyzer with the fine-tuned model."""
        self.model_path = model_path
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model = None
        self.tokenizer = None
        self.label_mapping = {0: "Negative", 1: "Neutral", 2: "Positive"}
        self.sentiment_colors = {
            "Negative": "#dc3545",  # Red
            "Neutral": "#6c757d",   # Gray
            "Positive": "#28a745"   # Green
        }
        self.load_model()

    def load_model(self):
        """Load the fine-tuned FinBERT model."""
        try:
            logger.info(f"Loading fine-tuned FinBERT model from {self.model_path}")

            # Load tokenizer and model
            self.tokenizer = AutoTokenizer.from_pretrained(self.model_path)
            self.model = AutoModelForSequenceClassification.from_pretrained(self.model_path)
            self.model.to(self.device)
            self.model.eval()

            logger.info("✅ Model loaded successfully")

        except Exception as e:
            logger.error(f"❌ Error loading model: {e}")
            raise

    def analyze_sentiment(self, text: str) -> Dict:
        """Analyze sentiment of the given text."""
        try:
            # Tokenize input
            inputs = self.tokenizer(
                text,
                return_tensors="pt",
                truncation=True,
                padding=True,
                max_length=512
            )

            # Move to device
            inputs = {k: v.to(self.device) for k, v in inputs.items()}

            # Get predictions
            with torch.no_grad():
                outputs = self.model(**inputs)
                logits = outputs.logits
                probabilities = torch.nn.functional.softmax(logits, dim=-1)

            # Extract results
            probs = probabilities.cpu().numpy()[0]
            predicted_class = np.argmax(probs)
            predicted_label = self.label_mapping[predicted_class]
            confidence = float(probs[predicted_class])

            return {
                'sentiment': predicted_label,
                'confidence': confidence,
                'probabilities': {
                    'Negative': float(probs[0]),
                    'Neutral': float(probs[1]),
                    'Positive': float(probs[2])
                },
                'color': self.sentiment_colors[predicted_label]
            }

        except Exception as e:
            logger.error(f"Error in sentiment analysis: {e}")
            raise

    def generate_summary(self, text: str, max_sentences: int = 3) -> str:
        """Generate a summary of the article."""
        try:
            # Simple extractive summarization based on sentence importance
            sentences = re.split(r'[.!?]+', text)
            sentences = [s.strip() for s in sentences if len(s.strip()) > 20]

            if len(sentences) <= max_sentences:
                return text

            # Score sentences based on financial keywords and length
            financial_keywords = [
                'revenue', 'profit', 'earnings', 'growth', 'stock', 'market',
                'investment', 'financial', 'company', 'business', 'sales',
                'income', 'loss', 'gain', 'performance', 'quarter', 'annual'
            ]

            sentence_scores = []
            for sentence in sentences:
                score = 0
                words = sentence.lower().split()

                # Score based on financial keywords
                for keyword in financial_keywords:
                    if keyword in sentence.lower():
                        score += 2

                # Prefer sentences of moderate length
                if 10 <= len(words) <= 30:
                    score += 1

                sentence_scores.append((sentence, score))

            # Sort by score and take top sentences
            sentence_scores.sort(key=lambda x: x[1], reverse=True)
            top_sentences = [s[0] for s in sentence_scores[:max_sentences]]

            # Maintain original order
            summary_sentences = []
            for sentence in sentences:
                if sentence in top_sentences:
                    summary_sentences.append(sentence)
                if len(summary_sentences) >= max_sentences:
                    break

            return '. '.join(summary_sentences) + '.'

        except Exception as e:
            logger.error(f"Error generating summary: {e}")
            return text[:500] + "..." if len(text) > 500 else text

    def generate_justification(self, text: str, sentiment: str, confidence: float) -> str:
        """Generate justification for the sentiment prediction."""
        try:
            # Define sentiment indicators
            positive_indicators = [
                'growth', 'increase', 'profit', 'gain', 'success', 'strong',
                'positive', 'up', 'rise', 'improve', 'better', 'good',
                'excellent', 'outstanding', 'record', 'beat', 'exceed'
            ]

            negative_indicators = [
                'loss', 'decline', 'decrease', 'fall', 'drop', 'weak',
                'negative', 'down', 'poor', 'bad', 'worse', 'concern',
                'risk', 'challenge', 'problem', 'miss', 'below'
            ]

            neutral_indicators = [
                'stable', 'maintain', 'steady', 'unchanged', 'flat',
                'similar', 'expected', 'in line', 'as planned'
            ]

            text_lower = text.lower()
            found_indicators = []

            if sentiment == "Positive":
                indicators = positive_indicators
                sentiment_desc = "bullish"
            elif sentiment == "Negative":
                indicators = negative_indicators
                sentiment_desc = "bearish"
            else:
                indicators = neutral_indicators
                sentiment_desc = "neutral"

            # Find indicators in text
            for indicator in indicators:
                if indicator in text_lower:
                    found_indicators.append(indicator)

            # Generate justification
            justification = f"The model classified this text as **{sentiment}** ({sentiment_desc}) "
            justification += f"with {confidence:.1%} confidence. "

            if found_indicators:
                justification += f"Key indicators include: {', '.join(found_indicators[:5])}. "

            # Add confidence interpretation
            if confidence > 0.8:
                justification += "The high confidence suggests clear sentiment indicators in the text."
            elif confidence > 0.6:
                justification += "The moderate confidence indicates some ambiguity in the sentiment."
            else:
                justification += "The lower confidence suggests mixed or unclear sentiment signals."

            return justification

        except Exception as e:
            logger.error(f"Error generating justification: {e}")
            return f"The model classified this text as {sentiment} with {confidence:.1%} confidence."

# Initialize the analyzer
MODEL_PATH = os.path.join(os.path.dirname(__file__), '..', 'models', 'final', 'finbert_pretrained')

def initialize_analyzer():
    """Initialize the FinBERT analyzer with fallback options."""
    try:
        # Try to load fine-tuned model first
        if os.path.exists(MODEL_PATH) and os.path.exists(os.path.join(MODEL_PATH, 'model.safetensors')):
            analyzer = FinBERTAnalyzer(MODEL_PATH)
            logger.info("✅ Fine-tuned FinBERT model loaded successfully")
            return analyzer, "fine-tuned"
        else:
            logger.warning("⚠️ Fine-tuned model not found. Using base FinBERT model.")
            logger.info("💡 To use the fine-tuned model, run: python download_model.py")

            # Fallback to base FinBERT model
            analyzer = FinBERTAnalyzer("ProsusAI/finbert")
            logger.info("✅ Base FinBERT model loaded successfully")
            return analyzer, "base"

    except Exception as e:
        logger.error(f"❌ Failed to initialize analyzer: {e}")
        return None, "failed"

analyzer, model_type = initialize_analyzer()

@app.route('/')
def home():
    """Main page with input form."""
    return render_template('index.html', model_type=model_type)

@app.route('/analyze', methods=['POST'])
def analyze():
    """Analyze financial text and return results."""
    try:
        if not analyzer:
            return jsonify({'error': 'Model not loaded. Please check server logs.'}), 500

        # Get input text
        text = request.form.get('text', '').strip()
        if not text:
            return jsonify({'error': 'Please provide text to analyze.'}), 400

        if len(text) < 10:
            return jsonify({'error': 'Text too short. Please provide at least 10 characters.'}), 400

        # Perform analysis
        sentiment_result = analyzer.analyze_sentiment(text)
        summary = analyzer.generate_summary(text)
        justification = analyzer.generate_justification(
            text,
            sentiment_result['sentiment'],
            sentiment_result['confidence']
        )

        # Prepare response
        response = {
            'success': True,
            'sentiment': sentiment_result['sentiment'],
            'confidence': sentiment_result['confidence'],
            'probabilities': sentiment_result['probabilities'],
            'color': sentiment_result['color'],
            'summary': summary,
            'justification': justification,
            'analysis_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'text_length': len(text),
            'word_count': len(text.split())
        }

        return jsonify(response)

    except Exception as e:
        logger.error(f"Error in analysis: {e}")
        return jsonify({'error': f'Analysis failed: {str(e)}'}), 500

@app.route('/api/analyze', methods=['POST'])
def api_analyze():
    """API endpoint for programmatic access."""
    try:
        if not analyzer:
            return jsonify({'error': 'Model not loaded'}), 500

        data = request.get_json()
        if not data or 'text' not in data:
            return jsonify({'error': 'Missing text field in JSON'}), 400

        text = data['text'].strip()
        if not text:
            return jsonify({'error': 'Empty text provided'}), 400

        # Perform analysis
        sentiment_result = analyzer.analyze_sentiment(text)
        summary = analyzer.generate_summary(text)
        justification = analyzer.generate_justification(
            text,
            sentiment_result['sentiment'],
            sentiment_result['confidence']
        )

        return jsonify({
            'sentiment': sentiment_result['sentiment'],
            'confidence': sentiment_result['confidence'],
            'probabilities': sentiment_result['probabilities'],
            'summary': summary,
            'justification': justification
        })

    except Exception as e:
        logger.error(f"API Error: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/health')
def health():
    """Health check endpoint."""
    return jsonify({
        'status': 'healthy' if analyzer else 'unhealthy',
        'model_loaded': analyzer is not None,
        'timestamp': datetime.now().isoformat()
    })

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)

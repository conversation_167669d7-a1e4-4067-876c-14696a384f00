"""
Helper utilities for FinBERT training and evaluation.
"""

import os
import json
import pickle
import torch
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Optional, Tuple, Any
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

def set_random_seed(seed: int = 42):
    """
    Set random seed for reproducibility.
    
    Args:
        seed: Random seed value
    """
    import random
    
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)
        # For deterministic behavior
        torch.backends.cudnn.deterministic = True
        torch.backends.cudnn.benchmark = False
    
    logger.info(f"Random seed set to {seed}")

def save_model_package(model, 
                      tokenizer, 
                      config: Dict,
                      metrics: Dict,
                      save_dir: str,
                      model_name: str = "finbert_sentiment"):
    """
    Save complete model package with metadata.
    
    Args:
        model: Trained model
        tokenizer: Tokenizer
        config: Training configuration
        metrics: Model performance metrics
        save_dir: Directory to save the model
        model_name: Name for the model
    """
    # Create save directory
    model_path = os.path.join(save_dir, model_name)
    os.makedirs(model_path, exist_ok=True)
    
    # Save model and tokenizer (Hugging Face format)
    model.save_pretrained(model_path)
    tokenizer.save_pretrained(model_path)
    
    # Save model metadata
    metadata = {
        "model_name": model_name,
        "model_type": "FinBERT",
        "num_labels": config.get("num_labels", 3),
        "max_length": config.get("max_length", 512),
        "label_mapping": config.get("label_mapping", {0: "Negative", 1: "Neutral", 2: "Positive"}),
        "training_config": config,
        "performance_metrics": metrics,
        "save_timestamp": datetime.now().isoformat(),
        "pytorch_version": torch.__version__,
        "device": str(model.device) if hasattr(model, 'device') else "unknown"
    }
    
    with open(os.path.join(model_path, "model_metadata.json"), "w") as f:
        json.dump(metadata, f, indent=2, default=str)
    
    # Save PyTorch checkpoint as backup
    checkpoint = {
        "model_state_dict": model.state_dict(),
        "config": config,
        "metrics": metrics,
        "metadata": metadata
    }
    torch.save(checkpoint, os.path.join(model_path, "pytorch_model.bin"))
    
    logger.info(f"Model package saved to {model_path}")

def load_model_package(model_path: str):
    """
    Load complete model package.
    
    Args:
        model_path: Path to the saved model
        
    Returns:
        Tuple of (model, tokenizer, metadata)
    """
    from transformers import AutoModelForSequenceClassification, AutoTokenizer
    
    logger.info(f"Loading model package from {model_path}")
    
    # Load model and tokenizer
    model = AutoModelForSequenceClassification.from_pretrained(model_path)
    tokenizer = AutoTokenizer.from_pretrained(model_path)
    
    # Load metadata
    metadata_path = os.path.join(model_path, "model_metadata.json")
    metadata = {}
    if os.path.exists(metadata_path):
        with open(metadata_path, "r") as f:
            metadata = json.load(f)
    
    logger.info("Model package loaded successfully")
    return model, tokenizer, metadata

def plot_training_curves(train_losses: List[float],
                        val_losses: Optional[List[float]] = None,
                        train_metrics: Optional[Dict[str, List[float]]] = None,
                        val_metrics: Optional[Dict[str, List[float]]] = None,
                        save_path: Optional[str] = None):
    """
    Plot training curves for loss and metrics.
    
    Args:
        train_losses: Training losses
        val_losses: Validation losses
        train_metrics: Training metrics
        val_metrics: Validation metrics
        save_path: Path to save the plot
    """
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('Training Curves', fontsize=16)
    
    # Plot 1: Loss curves
    axes[0, 0].plot(train_losses, label='Training Loss', color='blue')
    if val_losses:
        axes[0, 0].plot(val_losses, label='Validation Loss', color='red')
    axes[0, 0].set_title('Loss Curves')
    axes[0, 0].set_xlabel('Epoch')
    axes[0, 0].set_ylabel('Loss')
    axes[0, 0].legend()
    axes[0, 0].grid(True)
    
    # Plot 2: Accuracy curves
    if train_metrics and 'accuracy' in train_metrics:
        axes[0, 1].plot(train_metrics['accuracy'], label='Training Accuracy', color='blue')
    if val_metrics and 'accuracy' in val_metrics:
        axes[0, 1].plot(val_metrics['accuracy'], label='Validation Accuracy', color='red')
    axes[0, 1].set_title('Accuracy Curves')
    axes[0, 1].set_xlabel('Epoch')
    axes[0, 1].set_ylabel('Accuracy')
    axes[0, 1].legend()
    axes[0, 1].grid(True)
    
    # Plot 3: F1 Score curves
    if train_metrics and 'f1' in train_metrics:
        axes[1, 0].plot(train_metrics['f1'], label='Training F1', color='blue')
    if val_metrics and 'f1' in val_metrics:
        axes[1, 0].plot(val_metrics['f1'], label='Validation F1', color='red')
    axes[1, 0].set_title('F1 Score Curves')
    axes[1, 0].set_xlabel('Epoch')
    axes[1, 0].set_ylabel('F1 Score')
    axes[1, 0].legend()
    axes[1, 0].grid(True)
    
    # Plot 4: Learning Rate (if available)
    if train_metrics and 'learning_rate' in train_metrics:
        axes[1, 1].plot(train_metrics['learning_rate'], color='green')
        axes[1, 1].set_title('Learning Rate Schedule')
        axes[1, 1].set_xlabel('Step')
        axes[1, 1].set_ylabel('Learning Rate')
        axes[1, 1].grid(True)
    else:
        axes[1, 1].text(0.5, 0.5, 'Learning Rate\nNot Available', 
                       ha='center', va='center', transform=axes[1, 1].transAxes)
        axes[1, 1].set_title('Learning Rate Schedule')
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        logger.info(f"Training curves saved to {save_path}")
    
    plt.show()

def plot_confusion_matrix(confusion_matrix: np.ndarray,
                         class_names: List[str],
                         title: str = "Confusion Matrix",
                         save_path: Optional[str] = None):
    """
    Plot confusion matrix.
    
    Args:
        confusion_matrix: Confusion matrix array
        class_names: Names of the classes
        title: Title for the plot
        save_path: Path to save the plot
    """
    plt.figure(figsize=(8, 6))
    
    # Calculate percentages
    cm_percentage = confusion_matrix.astype('float') / confusion_matrix.sum(axis=1)[:, np.newaxis] * 100
    
    # Create heatmap
    sns.heatmap(confusion_matrix, annot=True, fmt='d', cmap='Blues',
                xticklabels=class_names, yticklabels=class_names)
    
    plt.title(title)
    plt.xlabel('Predicted Label')
    plt.ylabel('True Label')
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        logger.info(f"Confusion matrix saved to {save_path}")
    
    plt.show()
    
    # Also plot percentage version
    plt.figure(figsize=(8, 6))
    sns.heatmap(cm_percentage, annot=True, fmt='.1f', cmap='Blues',
                xticklabels=class_names, yticklabels=class_names)
    plt.title(f"{title} (Percentages)")
    plt.xlabel('Predicted Label')
    plt.ylabel('True Label')
    
    if save_path:
        percentage_path = save_path.replace('.png', '_percentage.png')
        plt.savefig(percentage_path, dpi=300, bbox_inches='tight')
        logger.info(f"Confusion matrix (percentage) saved to {percentage_path}")
    
    plt.show()

def plot_label_distribution(label_counts: Dict[str, int],
                           title: str = "Label Distribution",
                           save_path: Optional[str] = None):
    """
    Plot label distribution.
    
    Args:
        label_counts: Dictionary with label counts
        title: Title for the plot
        save_path: Path to save the plot
    """
    plt.figure(figsize=(10, 6))
    
    labels = list(label_counts.keys())
    counts = list(label_counts.values())
    
    bars = plt.bar(labels, counts, color=['red', 'gray', 'green'])
    plt.title(title)
    plt.xlabel('Sentiment')
    plt.ylabel('Number of Samples')
    
    # Add value labels on bars
    for bar, count in zip(bars, counts):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(counts)*0.01,
                str(count), ha='center', va='bottom')
    
    # Add percentage labels
    total = sum(counts)
    for bar, count in zip(bars, counts):
        percentage = count / total * 100
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height()/2,
                f'{percentage:.1f}%', ha='center', va='center', fontweight='bold')
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        logger.info(f"Label distribution plot saved to {save_path}")
    
    plt.show()

def get_device_info() -> Dict[str, Any]:
    """
    Get device information for logging.
    
    Returns:
        Dictionary with device information
    """
    device_info = {
        "cuda_available": torch.cuda.is_available(),
        "device_count": torch.cuda.device_count() if torch.cuda.is_available() else 0,
        "current_device": torch.cuda.current_device() if torch.cuda.is_available() else None,
    }
    
    if torch.cuda.is_available():
        device_info.update({
            "device_name": torch.cuda.get_device_name(),
            "memory_allocated": torch.cuda.memory_allocated(),
            "memory_reserved": torch.cuda.memory_reserved(),
            "cuda_version": torch.version.cuda
        })
    
    return device_info

def create_directory_structure(base_dir: str):
    """
    Create the complete directory structure for the project.
    
    Args:
        base_dir: Base directory path
    """
    directories = [
        "src/model",
        "src/data",
        "src/utils",
        "src/config",
        "notebooks",
        "data/raw",
        "data/processed",
        "models/checkpoints",
        "models/final",
        "results/metrics",
        "results/plots",
        "scripts",
        "logs",
        "deployment"
    ]
    
    for directory in directories:
        full_path = os.path.join(base_dir, directory)
        os.makedirs(full_path, exist_ok=True)
    
    logger.info(f"Directory structure created in {base_dir}")

def estimate_training_time(num_samples: int,
                          batch_size: int,
                          num_epochs: int,
                          estimated_seconds_per_batch: float = 1.0) -> Dict[str, float]:
    """
    Estimate training time.
    
    Args:
        num_samples: Number of training samples
        batch_size: Batch size
        num_epochs: Number of epochs
        estimated_seconds_per_batch: Estimated seconds per batch
        
    Returns:
        Dictionary with time estimates
    """
    batches_per_epoch = num_samples / batch_size
    total_batches = batches_per_epoch * num_epochs
    estimated_total_seconds = total_batches * estimated_seconds_per_batch
    
    return {
        "batches_per_epoch": batches_per_epoch,
        "total_batches": total_batches,
        "estimated_seconds": estimated_total_seconds,
        "estimated_minutes": estimated_total_seconds / 60,
        "estimated_hours": estimated_total_seconds / 3600
    }

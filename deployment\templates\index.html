<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FinBERT Financial Sentiment Analyzer</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/professional.css') }}" rel="stylesheet">
    <style>
        :root {
            --primary-black: #000000;
            --secondary-black: #1a1a1a;
            --tertiary-black: #2d2d2d;
            --light-gray: #f8f9fa;
            --medium-gray: #6c757d;
            --dark-gray: #343a40;
            --border-gray: #dee2e6;
            --accent-gray: #495057;
            --white: #ffffff;
            --success-green: #28a745;
            --danger-red: #dc3545;
            --warning-orange: #fd7e14;
        }

        body {
            background: linear-gradient(135deg, var(--light-gray) 0%, var(--white) 100%);
            min-height: 100vh;
            font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
            color: var(--primary-black);
            line-height: 1.6;
        }

        .main-container {
            background: var(--white);
            border-radius: 0;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            margin: 0;
            max-width: 100%;
            min-height: 100vh;
            border: 1px solid var(--border-gray);
        }

        .header {
            background: var(--primary-black);
            color: var(--white);
            padding: 2rem;
            border-radius: 0;
            text-align: left;
            border-bottom: 3px solid var(--secondary-black);
            position: relative;
        }

        .header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 1px;
            background: linear-gradient(90deg, var(--medium-gray) 0%, transparent 100%);
        }

        .logo-container {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
        }

        .logo {
            height: 48px;
            width: auto;
            margin-right: 1rem;
            filter: brightness(0) invert(1);
        }

        .header h1 {
            font-size: 2.2rem;
            font-weight: 700;
            margin: 0;
            letter-spacing: -0.02em;
        }

        .header p {
            font-size: 1.1rem;
            margin: 0.5rem 0 0 0;
            color: var(--medium-gray);
            font-weight: 400;
        }

        .header small {
            color: var(--medium-gray);
            font-size: 0.9rem;
            font-weight: 500;
        }

        .content-area {
            padding: 3rem 2rem;
            background: var(--white);
        }

        .form-control {
            border-radius: 4px;
            border: 2px solid var(--border-gray);
            transition: all 0.2s ease;
            font-family: inherit;
            font-size: 1rem;
            padding: 0.75rem 1rem;
            background: var(--white);
            color: var(--primary-black);
        }

        .form-control:focus {
            border-color: var(--primary-black);
            box-shadow: 0 0 0 0.2rem rgba(0, 0, 0, 0.1);
            outline: none;
            background: var(--white);
        }

        .form-control::placeholder {
            color: var(--medium-gray);
        }

        .btn-analyze {
            background: var(--primary-black);
            border: 2px solid var(--primary-black);
            border-radius: 4px;
            padding: 0.875rem 2rem;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.2s ease;
            color: var(--white);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .btn-analyze:hover {
            background: var(--secondary-black);
            border-color: var(--secondary-black);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            color: var(--white);
        }

        .btn-analyze:active {
            transform: translateY(0);
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
        }
        .results-container {
            background: var(--white);
            border-radius: 4px;
            padding: 2rem;
            margin-top: 2rem;
            border: 2px solid var(--border-gray);
            border-left: 4px solid var(--primary-black);
        }

        .section-title {
            color: var(--primary-black);
            font-weight: 700;
            font-size: 1.1rem;
            margin-bottom: 1rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .sentiment-badge {
            font-size: 1.1rem;
            padding: 0.75rem 1.5rem;
            border-radius: 4px;
            font-weight: 700;
            display: inline-block;
            margin-bottom: 1rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            border: 2px solid;
        }

        .sentiment-positive {
            background: var(--white);
            color: var(--success-green);
            border-color: var(--success-green);
        }

        .sentiment-negative {
            background: var(--white);
            color: var(--danger-red);
            border-color: var(--danger-red);
        }

        .sentiment-neutral {
            background: var(--white);
            color: var(--medium-gray);
            border-color: var(--medium-gray);
        }

        .confidence-bar {
            height: 8px;
            border-radius: 0;
            background: var(--border-gray);
            overflow: hidden;
            margin: 1rem 0;
            border: 1px solid var(--border-gray);
        }

        .confidence-fill {
            height: 100%;
            transition: width 0.3s ease;
            background: var(--primary-black);
        }

        .confidence-text {
            font-weight: 600;
            color: var(--primary-black);
            font-size: 0.9rem;
        }

        .probability-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid var(--border-gray);
            font-weight: 500;
        }

        .probability-item:last-child {
            border-bottom: none;
        }

        .probability-bar {
            width: 120px;
            height: 6px;
            background: var(--border-gray);
            border-radius: 0;
            overflow: hidden;
            margin-left: 1rem;
            border: 1px solid var(--border-gray);
        }

        .loading {
            display: none;
            text-align: center;
            padding: 3rem;
            background: var(--white);
            border: 2px solid var(--border-gray);
            margin-top: 2rem;
        }

        .spinner {
            border: 3px solid var(--border-gray);
            border-top: 3px solid var(--primary-black);
            border-radius: 50%;
            width: 32px;
            height: 32px;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading p {
            color: var(--medium-gray);
            font-weight: 500;
            margin: 0;
        }
        .example-texts {
            background: var(--light-gray);
            border-radius: 4px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            border: 1px solid var(--border-gray);
        }

        .example-texts small {
            color: var(--medium-gray);
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-size: 0.8rem;
        }

        .example-text {
            cursor: pointer;
            padding: 0.75rem 1rem;
            margin: 0.5rem 0.25rem 0 0;
            background: var(--white);
            border-radius: 4px;
            border: 1px solid var(--border-gray);
            display: inline-block;
            transition: all 0.2s ease;
            font-size: 0.9rem;
            color: var(--primary-black);
            font-weight: 500;
        }

        .example-text:hover {
            background: var(--primary-black);
            color: var(--white);
            border-color: var(--primary-black);
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 1rem;
            margin-top: 2rem;
        }

        .stat-card {
            background: var(--white);
            padding: 1.5rem 1rem;
            border-radius: 4px;
            text-align: center;
            border: 2px solid var(--border-gray);
            transition: all 0.2s ease;
        }

        .stat-card:hover {
            border-color: var(--primary-black);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-black);
            margin-bottom: 0.25rem;
        }

        .stat-label {
            color: var(--medium-gray);
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .form-text {
            color: var(--medium-gray);
            font-size: 0.9rem;
            margin-top: 0.5rem;
        }

        .form-text i {
            margin-right: 0.25rem;
        }

        .summary-box, .justification-box {
            background: var(--light-gray);
            border: 1px solid var(--border-gray);
            border-radius: 4px;
            padding: 1.5rem;
            color: var(--primary-black);
            font-size: 0.95rem;
            line-height: 1.6;
        }

        .analysis-section {
            margin-bottom: 2rem;
        }

        .analysis-section:last-child {
            margin-bottom: 0;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="main-container">
            <div class="header">
                <div class="logo-container">
                    <img src="logo.png" alt="Company Logo" class="logo">
                    <div>
                        <h1>FinBERT Sentiment Analyzer</h1>
                        <p class="mb-0">Professional Financial Text Analysis</p>
                    </div>
                </div>
                <small>Powered by Fine-tuned FinBERT Model | 83.8% Accuracy | Real-time Analysis</small>
            </div>
            <div class="content-area">
                <div class="row">
                    <div class="col-12">
                        <h3 class="section-title">Financial Text Analysis</h3>
                        <div class="example-texts">
                            <small><i class="fas fa-lightbulb me-1"></i>Sample Financial Texts:</small><br>
                            <span class="example-text" onclick="setExampleText(this)">
                                The company reported record quarterly earnings with revenue growth of 25% year-over-year.
                            </span>
                            <span class="example-text" onclick="setExampleText(this)">
                                Stock prices fell sharply after the company announced significant losses and layoffs.
                            </span>
                            <span class="example-text" onclick="setExampleText(this)">
                                The quarterly results were in line with analyst expectations, showing stable performance.
                            </span>
                        </div>
                        <form id="analysisForm">
                            <div class="mb-3">
                                <textarea 
                                    class="form-control" 
                                    id="textInput" 
                                    rows="6" 
                                    placeholder="Paste your financial news article, earnings report, or market analysis here..."
                                    required
                                ></textarea>
                                <div class="form-text">
                                    <i class="fas fa-info-circle me-1"></i>
                                    Enter financial text (minimum 10 characters). The model works best with financial news, earnings reports, and market analysis.
                                </div>
                            </div>
                            <div class="text-center">
                                <button type="submit" class="btn btn-primary btn-analyze">
                                    <i class="fas fa-brain me-2"></i>Analyze Sentiment
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                <div class="loading" id="loading">
                    <div class="spinner"></div>
                    <p>Analyzing financial sentiment...</p>
                </div>
                <div id="results" style="display: none;">
                    <div class="results-container">
                        <h3 class="section-title">Analysis Results</h3>
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <h5 class="section-title">Sentiment Classification</h5>
                                <div id="sentimentBadge" class="sentiment-badge"></div>
                                <div class="confidence-bar">
                                    <div id="confidenceBar" class="confidence-fill"></div>
                                </div>
                                <small class="confidence-text">Confidence: <span id="confidenceText"></span></small>
                            </div>
                            <div class="col-md-6">
                                <h5 class="section-title">Probability Distribution</h5>
                                <div id="probabilities"></div>
                            </div>
                        </div>
                        <div class="mb-4">
                            <h5 class="section-title">Executive Summary</h5>
                            <div id="summary" class="summary-box"></div>
                        </div>
                        <div class="mb-4">
                            <h5 class="section-title">Analysis Justification</h5>
                            <div id="justification" class="justification-box"></div>
                        </div>
                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-value" id="wordCount">-</div>
                                <div class="stat-label">Words</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value" id="charCount">-</div>
                                <div class="stat-label">Characters</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value" id="analysisTime">-</div>
                                <div class="stat-label">Analysis Time</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function setExampleText(element) {
            document.getElementById('textInput').value = element.textContent.trim();
        }
        document.getElementById('analysisForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            const text = document.getElementById('textInput').value.trim();
            if (!text) {
                alert('Please enter some text to analyze.');
                return;
            }
            document.getElementById('loading').style.display = 'block';
            document.getElementById('results').style.display = 'none';
            try {
                const formData = new FormData();
                formData.append('text', text);
                const response = await fetch('/analyze', {
                    method: 'POST',
                    body: formData
                });
                const data = await response.json();
                if (data.success) {
                    displayResults(data);
                } else {
                    alert('Error: ' + (data.error || 'Analysis failed'));
                }
            } catch (error) {
                alert('Error: ' + error.message);
            } finally {
                document.getElementById('loading').style.display = 'none';
            }
        });
        function displayResults(data) {
            const badge = document.getElementById('sentimentBadge');
            badge.textContent = data.sentiment;
            badge.className = `sentiment-badge sentiment-${data.sentiment.toLowerCase()}`;
            const confidenceBar = document.getElementById('confidenceBar');
            confidenceBar.style.width = (data.confidence * 100) + '%';
            document.getElementById('confidenceText').textContent = (data.confidence * 100).toFixed(1) + '%';
            const probContainer = document.getElementById('probabilities');
            probContainer.innerHTML = '';
            Object.entries(data.probabilities).forEach(([sentiment, prob]) => {
                const item = document.createElement('div');
                item.className = 'probability-item';
                item.innerHTML = `
                    <span>${sentiment}</span>
                    <div class="d-flex align-items-center">
                        <span class="me-2">${(prob * 100).toFixed(1)}%</span>
                        <div class="probability-bar">
                            <div style="width: ${prob * 100}%; height: 100%; background: ${getColorForSentiment(sentiment)};"></div>
                        </div>
                    </div>
                `;
                probContainer.appendChild(item);
            });
            document.getElementById('summary').textContent = data.summary;
            document.getElementById('justification').innerHTML = data.justification.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
            document.getElementById('wordCount').textContent = data.word_count;
            document.getElementById('charCount').textContent = data.text_length;
            document.getElementById('analysisTime').textContent = data.analysis_time;
            document.getElementById('results').style.display = 'block';
            document.getElementById('results').scrollIntoView({ behavior: 'smooth' });
        }
        function getColorForSentiment(sentiment) {
            const colors = {
                'Negative': '#dc3545',
                'Neutral': '#6c757d',
                'Positive': '#28a745'
            };
            return colors[sentiment] || '#6c757d';
        }
    </script>
</body>
</html>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Xcapitale Sentiment Analyzer</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/professional.css') }}" rel="stylesheet">
    <style>
        :root {
            --primary-black: #000000;
            --secondary-black: #1a1a1a;
            --tertiary-black: #2d2d2d;
            --light-gray: #f8f9fa;
            --medium-gray: #6c757d;
            --dark-gray: #343a40;
            --border-gray: #e9ecef;
            --accent-gray: #495057;
            --white: #ffffff;
            --success-green: #28a745;
            --danger-red: #dc3545;
            --warning-orange: #fd7e14;
            --shadow-light: rgba(0, 0, 0, 0.05);
            --shadow-medium: rgba(0, 0, 0, 0.1);
            --shadow-dark: rgba(0, 0, 0, 0.15);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: #fafbfc;
            min-height: 100vh;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            color: var(--primary-black);
            line-height: 1.6;
            font-size: 16px;
        }

        .main-container {
            background: var(--white);
            margin: 0;
            max-width: 100%;
            min-height: 100vh;
            box-shadow: 0 0 0 1px var(--border-gray);
        }

        /* Professional Header */
        .header {
            background: linear-gradient(135deg, #000000 0%, #1a1a1a 100%) !important;
            color: #ffffff !important;
            padding: 2.5rem 3rem;
            position: relative;
            overflow: hidden;
            z-index: 10;
            display: block !important;
            visibility: visible !important;
            min-height: 120px;
        }



        .logo-container {
            display: flex;
            align-items: center;
            margin-bottom: 1.5rem;
            position: relative;
            z-index: 2;
        }

        .logo-container * {
            color: #ffffff !important;
        }

        .logo {
            height: 80px;
            width: 80px;
            margin-right: 2rem;
            background: #333333;
            border-radius: 12px;
            padding: 12px;
            box-shadow: 0 4px 16px rgba(255, 255, 255, 0.2);
            object-fit: contain;
            border: 2px solid #555555;
        }

        .logo-fallback {
            height: 80px;
            width: 80px;
            margin-right: 2rem;
            background: #ffffff;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 800;
            font-size: 2rem;
            color: #000000;
            box-shadow: 0 4px 16px rgba(255, 255, 255, 0.2);
        }

        .brand-info h1 {
            font-size: 2.5rem;
            font-weight: 800;
            margin: 0;
            letter-spacing: -0.03em;
            color: #ffffff !important;
        }

        .brand-info p {
            font-size: 1.1rem;
            margin: 0.25rem 0 0 0;
            color: #cccccc !important;
            font-weight: 500;
        }

        .header-badge {
            position: absolute;
            top: 2.5rem;
            right: 3rem;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 0.5rem 1rem;
            font-size: 0.85rem;
            font-weight: 600;
            color: #adb5bd;
            backdrop-filter: blur(10px);
        }

        /* Content Area */
        .content-area {
            padding: 4rem 3rem;
            background: var(--white);
            max-width: 1200px;
            margin: 0 auto;
        }

        .section-title {
            color: var(--primary-black);
            font-weight: 700;
            font-size: 1.25rem;
            margin-bottom: 2rem;
            text-transform: uppercase;
            letter-spacing: 1px;
            position: relative;
            padding-bottom: 0.75rem;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 60px;
            height: 3px;
            background: var(--primary-black);
        }

        /* Example Texts */
        .example-texts {
            background: #f8f9fa;
            border: 1px solid var(--border-gray);
            border-radius: 8px;
            padding: 2rem;
            margin-bottom: 3rem;
        }

        .example-texts small {
            color: var(--medium-gray);
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-size: 0.8rem;
            margin-bottom: 1rem;
            display: block;
        }

        .example-text {
            cursor: pointer;
            padding: 1rem 1.25rem;
            margin: 0.75rem 0.5rem 0 0;
            background: var(--white);
            border-radius: 6px;
            border: 1px solid var(--border-gray);
            display: inline-block;
            transition: all 0.3s ease;
            font-size: 0.95rem;
            color: var(--primary-black);
            font-weight: 500;
            box-shadow: 0 2px 4px var(--shadow-light);
        }

        .example-text:hover {
            background: var(--primary-black);
            color: var(--white);
            border-color: var(--primary-black);
            transform: translateY(-2px);
            box-shadow: 0 8px 20px var(--shadow-medium);
        }

        /* Form Controls */
        .form-control {
            border-radius: 8px;
            border: 2px solid var(--border-gray);
            transition: all 0.3s ease;
            font-family: inherit;
            font-size: 1rem;
            padding: 1rem 1.25rem;
            background: var(--white);
            color: var(--primary-black);
            box-shadow: 0 2px 4px var(--shadow-light);
            min-height: 120px;
            resize: vertical;
        }

        .form-control:focus {
            border-color: var(--primary-black);
            box-shadow: 0 0 0 0.25rem rgba(0, 0, 0, 0.1), 0 4px 12px var(--shadow-medium);
            outline: none;
            background: var(--white);
        }

        .form-control::placeholder {
            color: var(--medium-gray);
            font-style: italic;
        }

        .btn-analyze {
            background: var(--primary-black);
            border: 2px solid var(--primary-black);
            border-radius: 8px;
            padding: 1rem 3rem;
            font-weight: 700;
            font-size: 1rem;
            transition: all 0.3s ease;
            color: var(--white);
            text-transform: uppercase;
            letter-spacing: 1px;
            box-shadow: 0 4px 12px var(--shadow-medium);
            position: relative;
            overflow: hidden;
        }

        .btn-analyze::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn-analyze:hover::before {
            left: 100%;
        }

        .btn-analyze:hover {
            background: var(--secondary-black);
            border-color: var(--secondary-black);
            transform: translateY(-2px);
            box-shadow: 0 8px 24px var(--shadow-dark);
            color: var(--white);
        }

        .btn-analyze:active {
            transform: translateY(0);
            box-shadow: 0 4px 12px var(--shadow-medium);
        }
        /* Results Container */
        .results-container {
            background: var(--white);
            border-radius: 12px;
            padding: 3rem;
            margin-top: 3rem;
            border: 1px solid var(--border-gray);
            box-shadow: 0 8px 32px var(--shadow-light);
            position: relative;
        }

        .results-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-black) 0%, var(--secondary-black) 100%);
            border-radius: 12px 12px 0 0;
        }

        /* Sentiment Badges */
        .sentiment-badge {
            font-size: 1.1rem;
            padding: 1rem 2rem;
            border-radius: 8px;
            font-weight: 700;
            display: inline-flex;
            align-items: center;
            margin-bottom: 1.5rem;
            text-transform: uppercase;
            letter-spacing: 1px;
            border: 2px solid;
            box-shadow: 0 4px 12px var(--shadow-light);
            transition: all 0.3s ease;
        }

        .sentiment-badge::before {
            content: '';
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 0.75rem;
        }

        .sentiment-bullish {
            background: rgba(40, 167, 69, 0.05);
            color: var(--success-green);
            border-color: var(--success-green);
        }

        .sentiment-bullish::before {
            background: var(--success-green);
        }

        .sentiment-bearish {
            background: rgba(220, 53, 69, 0.05);
            color: var(--danger-red);
            border-color: var(--danger-red);
        }

        .sentiment-bearish::before {
            background: var(--danger-red);
        }

        .sentiment-neutral {
            background: rgba(108, 117, 125, 0.05);
            color: var(--medium-gray);
            border-color: var(--medium-gray);
        }

        .sentiment-neutral::before {
            background: var(--medium-gray);
        }

        /* Confidence Bar */
        .confidence-bar {
            height: 12px;
            border-radius: 6px;
            background: #f1f3f4;
            overflow: hidden;
            margin: 1.5rem 0;
            box-shadow: inset 0 2px 4px var(--shadow-light);
            position: relative;
        }

        .confidence-fill {
            height: 100%;
            transition: width 1s ease-out;
            background: linear-gradient(90deg, var(--primary-black) 0%, var(--secondary-black) 100%);
            border-radius: 6px;
            position: relative;
            overflow: hidden;
        }

        .confidence-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .confidence-text {
            font-weight: 700;
            color: var(--primary-black);
            font-size: 1rem;
        }

        /* Probability Items */
        .probability-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 0;
            border-bottom: 1px solid #f1f3f4;
            font-weight: 600;
        }

        .probability-item:last-child {
            border-bottom: none;
        }

        .probability-bar {
            width: 150px;
            height: 8px;
            background: #f1f3f4;
            border-radius: 4px;
            overflow: hidden;
            margin-left: 1rem;
            box-shadow: inset 0 1px 2px var(--shadow-light);
        }

        /* Loading States */
        .loading {
            display: none;
            text-align: center;
            padding: 4rem;
            background: var(--white);
            border: 1px solid var(--border-gray);
            border-radius: 12px;
            margin-top: 3rem;
            box-shadow: 0 8px 32px var(--shadow-light);
        }

        .spinner {
            border: 4px solid #f1f3f4;
            border-top: 4px solid var(--primary-black);
            border-radius: 50%;
            width: 48px;
            height: 48px;
            animation: spin 1s linear infinite;
            margin: 0 auto 1.5rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading p {
            color: var(--medium-gray);
            font-weight: 600;
            margin: 0;
            font-size: 1.1rem;
        }

        /* Analysis Sections */
        .analysis-section {
            margin-bottom: 2.5rem;
            padding: 2rem;
            background: #fafbfc;
            border-radius: 8px;
            border: 1px solid #f1f3f4;
        }

        .analysis-section:last-child {
            margin-bottom: 0;
        }

        .summary-box, .justification-box {
            background: var(--white);
            border: 1px solid var(--border-gray);
            border-radius: 8px;
            padding: 2rem;
            color: var(--primary-black);
            font-size: 1rem;
            line-height: 1.7;
            box-shadow: 0 2px 8px var(--shadow-light);
        }
        /* Stats Grid */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-top: 3rem;
        }

        .stat-card {
            background: var(--white);
            padding: 2rem 1.5rem;
            border-radius: 12px;
            text-align: center;
            border: 1px solid var(--border-gray);
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px var(--shadow-light);
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-black) 0%, var(--secondary-black) 100%);
        }

        .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 32px var(--shadow-medium);
        }

        .stat-value {
            font-size: 2.5rem;
            font-weight: 800;
            color: var(--primary-black);
            margin-bottom: 0.5rem;
            line-height: 1;
        }

        .stat-label {
            color: var(--medium-gray);
            font-size: 0.85rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        /* Form Helper Text */
        .form-text {
            color: var(--medium-gray);
            font-size: 0.9rem;
            margin-top: 0.75rem;
            font-weight: 500;
        }

        .form-text i {
            margin-right: 0.5rem;
            color: var(--primary-black);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .header {
                padding: 2rem 1.5rem;
            }

            .logo-container {
                flex-direction: column;
                text-align: center;
                margin-bottom: 1rem;
            }

            .logo {
                margin-right: 0;
                margin-bottom: 1rem;
            }

            .header-badge {
                position: static;
                margin-top: 1rem;
                display: inline-block;
            }

            .brand-info h1 {
                font-size: 2rem;
            }

            .content-area {
                padding: 2rem 1.5rem;
            }

            .section-title {
                font-size: 1.1rem;
            }

            .example-text {
                display: block;
                margin: 0.5rem 0;
            }

            .stats-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .results-container {
                padding: 2rem 1.5rem;
            }

            .btn-analyze {
                width: 100%;
                padding: 1.25rem;
            }
        }

        @media (max-width: 480px) {
            .header {
                padding: 1.5rem 1rem;
            }

            .content-area {
                padding: 1.5rem 1rem;
            }

            .brand-info h1 {
                font-size: 1.75rem;
            }

            .sentiment-badge {
                font-size: 1rem;
                padding: 0.875rem 1.5rem;
            }

            .stat-value {
                font-size: 2rem;
            }
        }

        /* Print Styles */
        @media print {
            .header {
                background: var(--primary-black) !important;
                color: var(--white) !important;
            }

            .main-container {
                box-shadow: none;
                border: 1px solid var(--primary-black);
            }

            .btn-analyze, .example-texts {
                display: none;
            }

            .results-container {
                border: 2px solid var(--primary-black);
            }
        }

        /* Accessibility */
        .btn-analyze:focus,
        .form-control:focus,
        .example-text:focus {
            outline: 3px solid rgba(0, 0, 0, 0.3);
            outline-offset: 2px;
        }

        /* Dark mode support */
        @media (prefers-color-scheme: dark) {
            :root {
                --white: #1a1a1a;
                --light-gray: #2d2d2d;
                --border-gray: #404040;
                --primary-black: #ffffff;
                --secondary-black: #e9ecef;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="main-container">
            <div class="header">
                <div class="logo-container">
                    <img src="{{ url_for('logo') }}" alt="Xcapitale Logo" class="logo" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                    <div class="logo-fallback" style="display: none;">XC</div>
                    <div class="brand-info">
                        <h1>Xcapitale Sentiment Analyzer</h1>
                        <p>Professional Financial Text Analysis</p>
                    </div>
                </div>
            </div>
            <div class="content-area">
                <div class="row">
                    <div class="col-12">
                        <h3 class="section-title">Financial Sentiment Analyzer</h3>
                        <form id="analysisForm">
                            <div class="mb-3">
                                <textarea 
                                    class="form-control" 
                                    id="textInput" 
                                    rows="8"
                                    placeholder="Enter your financial news article, earnings report, market analysis, or investment commentary here...

Example: 'The company reported exceptional quarterly earnings that significantly exceeded analyst expectations, with revenue growth of 25% year-over-year and strong guidance for the upcoming quarter.'"
                                    required
                                ></textarea>
                                <div class="form-text">
                                    <i class="fas fa-info-circle me-1"></i>
                                    Enter financial text (minimum 10 characters). The model works best with financial news, earnings reports, and market analysis.
                                </div>
                            </div>
                            <div class="text-center">
                                <button type="submit" class="btn btn-primary btn-analyze">
                                    <i class="fas fa-chart-line me-2"></i>Analyze Sentiment
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                <div class="loading" id="loading">
                    <div class="spinner"></div>
                    <p>Analyzing financial sentiment...</p>
                </div>
                <div id="results" style="display: none;">
                    <div class="results-container">
                        <h3 class="section-title">Analysis Results</h3>
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <h5 class="section-title">Sentiment Classification</h5>
                                <div id="sentimentBadge" class="sentiment-badge"></div>
                                <div class="confidence-bar">
                                    <div id="confidenceBar" class="confidence-fill"></div>
                                </div>
                                <small class="confidence-text">Confidence: <span id="confidenceText"></span></small>
                            </div>
                            <div class="col-md-6">
                                <h5 class="section-title">Probability Distribution</h5>
                                <div id="probabilities"></div>
                            </div>
                        </div>
                        <div class="mb-4">
                            <h5 class="section-title">Executive Summary</h5>
                            <div id="summary" class="summary-box"></div>
                        </div>
                        <div class="mb-4">
                            <h5 class="section-title">Analysis Justification</h5>
                            <div id="justification" class="justification-box"></div>
                        </div>
                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-value" id="wordCount">-</div>
                                <div class="stat-label">Words</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value" id="charCount">-</div>
                                <div class="stat-label">Characters</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value" id="analysisTime">-</div>
                                <div class="stat-label">Analysis Time</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>

        document.getElementById('analysisForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            const text = document.getElementById('textInput').value.trim();
            if (!text) {
                alert('Please enter some text to analyze.');
                return;
            }
            document.getElementById('loading').style.display = 'block';
            document.getElementById('results').style.display = 'none';
            try {
                const formData = new FormData();
                formData.append('text', text);
                const response = await fetch('/analyze', {
                    method: 'POST',
                    body: formData
                });
                const data = await response.json();
                if (data.success) {
                    displayResults(data);
                } else {
                    alert('Error: ' + (data.error || 'Analysis failed'));
                }
            } catch (error) {
                alert('Error: ' + error.message);
            } finally {
                document.getElementById('loading').style.display = 'none';
            }
        });
        function displayResults(data) {
            const badge = document.getElementById('sentimentBadge');
            badge.textContent = data.sentiment;
            badge.className = `sentiment-badge sentiment-${data.sentiment.toLowerCase()}`;
            const confidenceBar = document.getElementById('confidenceBar');
            confidenceBar.style.width = (data.confidence * 100) + '%';
            document.getElementById('confidenceText').textContent = (data.confidence * 100).toFixed(1) + '%';
            const probContainer = document.getElementById('probabilities');
            probContainer.innerHTML = '';
            Object.entries(data.probabilities).forEach(([sentiment, prob]) => {
                const item = document.createElement('div');
                item.className = 'probability-item';

                item.innerHTML = `
                    <span>${sentiment}</span>
                    <div class="d-flex align-items-center">
                        <span class="me-2">${(prob * 100).toFixed(1)}%</span>
                        <div class="probability-bar">
                            <div style="width: ${prob * 100}%; height: 100%; background: ${getColorForSentiment(sentiment)};"></div>
                        </div>
                    </div>
                `;
                probContainer.appendChild(item);
            });
            document.getElementById('summary').textContent = data.summary;
            document.getElementById('justification').innerHTML = data.justification.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
            document.getElementById('wordCount').textContent = data.word_count;
            document.getElementById('charCount').textContent = data.text_length;
            document.getElementById('analysisTime').textContent = data.analysis_time;
            document.getElementById('results').style.display = 'block';
            document.getElementById('results').scrollIntoView({ behavior: 'smooth' });
        }
        function getColorForSentiment(sentiment) {
            const colors = {
                'Bearish': '#dc3545',
                'Neutral': '#6c757d',
                'Bullish': '#28a745'
            };
            return colors[sentiment] || '#6c757d';
        }
    </script>
</body>
</html>
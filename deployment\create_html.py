#!/usr/bin/env python3
# -*- coding: utf-8 -*-

html_content = """<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FinBERT Financial Sentiment Analyzer</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            margin: 2rem auto;
            max-width: 1200px;
        }
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 2rem;
            border-radius: 20px 20px 0 0;
            text-align: center;
        }
        .content-area {
            padding: 2rem;
        }
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-analyze {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-analyze:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        .results-container {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 2rem;
            margin-top: 2rem;
            border-left: 5px solid #667eea;
        }
        .sentiment-badge {
            font-size: 1.2rem;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: 600;
            display: inline-block;
            margin-bottom: 1rem;
        }
        .confidence-bar {
            height: 10px;
            border-radius: 5px;
            background: #e9ecef;
            overflow: hidden;
            margin: 10px 0;
        }
        .confidence-fill {
            height: 100%;
            transition: width 0.5s ease;
        }
        .probability-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        .probability-bar {
            width: 100px;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin-left: 10px;
        }
        .loading {
            display: none;
            text-align: center;
            padding: 2rem;
        }
        .spinner {
            border: 4px solid #f3f3f4;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .example-texts {
            background: #e3f2fd;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        .example-text {
            cursor: pointer;
            padding: 8px 12px;
            margin: 4px;
            background: white;
            border-radius: 6px;
            border: 1px solid #ddd;
            display: inline-block;
            transition: all 0.2s ease;
        }
        .example-text:hover {
            background: #667eea;
            color: white;
            transform: translateY(-1px);
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
        .stat-card {
            background: white;
            padding: 1rem;
            border-radius: 10px;
            text-align: center;
            border: 1px solid #e9ecef;
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 600;
            color: #667eea;
        }
        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="main-container">
            <div class="header">
                <h1><i class="fas fa-chart-line me-3"></i>FinBERT Financial Sentiment Analyzer</h1>
                <p class="mb-0">Advanced AI-powered analysis of financial news and articles</p>
                <small>Powered by Fine-tuned FinBERT Model | Accuracy: 83.8%</small>
            </div>
            <div class="content-area">
                <div class="row">
                    <div class="col-12">
                        <h3><i class="fas fa-edit me-2"></i>Enter Financial Text</h3>
                        <div class="example-texts">
                            <small class="text-muted"><i class="fas fa-lightbulb me-1"></i>Click on examples to try:</small><br>
                            <span class="example-text" onclick="setExampleText(this)">
                                The company reported record quarterly earnings with revenue growth of 25% year-over-year.
                            </span>
                            <span class="example-text" onclick="setExampleText(this)">
                                Stock prices fell sharply after the company announced significant losses and layoffs.
                            </span>
                            <span class="example-text" onclick="setExampleText(this)">
                                The quarterly results were in line with analyst expectations, showing stable performance.
                            </span>
                        </div>
                        <form id="analysisForm">
                            <div class="mb-3">
                                <textarea 
                                    class="form-control" 
                                    id="textInput" 
                                    rows="6" 
                                    placeholder="Paste your financial news article, earnings report, or market analysis here..."
                                    required
                                ></textarea>
                                <div class="form-text">
                                    <i class="fas fa-info-circle me-1"></i>
                                    Enter financial text (minimum 10 characters). The model works best with financial news, earnings reports, and market analysis.
                                </div>
                            </div>
                            <div class="text-center">
                                <button type="submit" class="btn btn-primary btn-analyze">
                                    <i class="fas fa-brain me-2"></i>Analyze Sentiment
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                <div class="loading" id="loading">
                    <div class="spinner"></div>
                    <p>Analyzing financial sentiment...</p>
                </div>
                <div id="results" style="display: none;">
                    <div class="results-container">
                        <h3><i class="fas fa-chart-bar me-2"></i>Analysis Results</h3>
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <h5>Sentiment Classification</h5>
                                <div id="sentimentBadge" class="sentiment-badge"></div>
                                <div class="confidence-bar">
                                    <div id="confidenceBar" class="confidence-fill"></div>
                                </div>
                                <small class="text-muted">Confidence: <span id="confidenceText"></span></small>
                            </div>
                            <div class="col-md-6">
                                <h5>Probability Distribution</h5>
                                <div id="probabilities"></div>
                            </div>
                        </div>
                        <div class="mb-4">
                            <h5><i class="fas fa-file-alt me-2"></i>Article Summary</h5>
                            <div id="summary" class="p-3 bg-light rounded"></div>
                        </div>
                        <div class="mb-4">
                            <h5><i class="fas fa-lightbulb me-2"></i>Analysis Justification</h5>
                            <div id="justification" class="p-3 bg-light rounded"></div>
                        </div>
                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-value" id="wordCount">-</div>
                                <div class="stat-label">Words</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value" id="charCount">-</div>
                                <div class="stat-label">Characters</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value" id="analysisTime">-</div>
                                <div class="stat-label">Analysis Time</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function setExampleText(element) {
            document.getElementById('textInput').value = element.textContent.trim();
        }
        document.getElementById('analysisForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            const text = document.getElementById('textInput').value.trim();
            if (!text) {
                alert('Please enter some text to analyze.');
                return;
            }
            document.getElementById('loading').style.display = 'block';
            document.getElementById('results').style.display = 'none';
            try {
                const formData = new FormData();
                formData.append('text', text);
                const response = await fetch('/analyze', {
                    method: 'POST',
                    body: formData
                });
                const data = await response.json();
                if (data.success) {
                    displayResults(data);
                } else {
                    alert('Error: ' + (data.error || 'Analysis failed'));
                }
            } catch (error) {
                alert('Error: ' + error.message);
            } finally {
                document.getElementById('loading').style.display = 'none';
            }
        });
        function displayResults(data) {
            const badge = document.getElementById('sentimentBadge');
            badge.textContent = data.sentiment;
            badge.style.backgroundColor = data.color;
            badge.style.color = 'white';
            const confidenceBar = document.getElementById('confidenceBar');
            confidenceBar.style.width = (data.confidence * 100) + '%';
            confidenceBar.style.backgroundColor = data.color;
            document.getElementById('confidenceText').textContent = (data.confidence * 100).toFixed(1) + '%';
            const probContainer = document.getElementById('probabilities');
            probContainer.innerHTML = '';
            Object.entries(data.probabilities).forEach(([sentiment, prob]) => {
                const item = document.createElement('div');
                item.className = 'probability-item';
                item.innerHTML = `
                    <span>${sentiment}</span>
                    <div class="d-flex align-items-center">
                        <span class="me-2">${(prob * 100).toFixed(1)}%</span>
                        <div class="probability-bar">
                            <div style="width: ${prob * 100}%; height: 100%; background: ${getColorForSentiment(sentiment)};"></div>
                        </div>
                    </div>
                `;
                probContainer.appendChild(item);
            });
            document.getElementById('summary').textContent = data.summary;
            document.getElementById('justification').innerHTML = data.justification.replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>');
            document.getElementById('wordCount').textContent = data.word_count;
            document.getElementById('charCount').textContent = data.text_length;
            document.getElementById('analysisTime').textContent = data.analysis_time;
            document.getElementById('results').style.display = 'block';
            document.getElementById('results').scrollIntoView({ behavior: 'smooth' });
        }
        function getColorForSentiment(sentiment) {
            const colors = {
                'Negative': '#dc3545',
                'Neutral': '#6c757d',
                'Positive': '#28a745'
            };
            return colors[sentiment] || '#6c757d';
        }
    </script>
</body>
</html>"""

# Write the file with proper UTF-8 encoding
with open('templates/index.html', 'w', encoding='utf-8') as f:
    f.write(html_content)

print('✅ HTML file created with proper UTF-8 encoding')

"""
Training configuration for FinBERT sentiment classification model.
Contains all hyperparameters and settings for training.
"""

import torch
from dataclasses import dataclass
from typing import Dict, List, Optional

@dataclass
class TrainingConfig:
    """Configuration class for FinBERT training parameters."""
    
    # Model configuration
    model_name: str = "ProsusAI/finbert"
    num_labels: int = 3
    max_length: int = 512
    
    # Training hyperparameters
    learning_rate: float = 2e-5
    batch_size: int = 16
    num_epochs: int = 3
    warmup_steps: int = 500
    weight_decay: float = 0.01
    adam_epsilon: float = 1e-8
    
    # Data configuration
    train_test_split: float = 0.8
    validation_split: float = 0.1
    random_seed: int = 42
    
    # Paths
    data_dir: str = "data"
    model_output_dir: str = "models"
    results_dir: str = "results"
    logs_dir: str = "logs"
    
    # Training settings
    evaluation_strategy: str = "epoch"
    save_strategy: str = "epoch"
    load_best_model_at_end: bool = True
    metric_for_best_model: str = "eval_f1"
    greater_is_better: bool = True
    
    # Device configuration
    device: str = "cuda" if torch.cuda.is_available() else "cpu"
    fp16: bool = torch.cuda.is_available()  # Use mixed precision if GPU available
    dataloader_num_workers: int = 2
    
    # Early stopping
    early_stopping_patience: int = 3
    early_stopping_threshold: float = 0.001
    
    # Label mapping
    label_mapping: Dict[int, str] = None
    
    def __post_init__(self):
        """Initialize default label mapping if not provided."""
        if self.label_mapping is None:
            self.label_mapping = {
                0: "Negative",
                1: "Neutral", 
                2: "Positive"
            }
    
    def get_training_args(self) -> Dict:
        """Get training arguments for Hugging Face Trainer."""
        return {
            "output_dir": f"{self.model_output_dir}/checkpoints",
            "num_train_epochs": self.num_epochs,
            "per_device_train_batch_size": self.batch_size,
            "per_device_eval_batch_size": self.batch_size,
            "learning_rate": self.learning_rate,
            "weight_decay": self.weight_decay,
            "warmup_steps": self.warmup_steps,
            "evaluation_strategy": self.evaluation_strategy,
            "save_strategy": self.save_strategy,
            "load_best_model_at_end": self.load_best_model_at_end,
            "metric_for_best_model": self.metric_for_best_model,
            "greater_is_better": self.greater_is_better,
            "fp16": self.fp16,
            "dataloader_num_workers": self.dataloader_num_workers,
            "logging_dir": f"{self.logs_dir}",
            "logging_steps": 50,
            "save_total_limit": 3,
            "seed": self.random_seed,
        }

# Default configuration instance
default_config = TrainingConfig()

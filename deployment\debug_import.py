#!/usr/bin/env python
"""
Debug script to test Flask app import
"""

try:
    print("Testing app import...")
    import app
    print(f"✅ app module imported successfully")
    print(f"Available attributes: {[attr for attr in dir(app) if not attr.startswith('_')]}")
    
    if hasattr(app, 'app'):
        print("✅ Flask app instance found")
        flask_app = app.app
        print(f"Flask app type: {type(flask_app)}")
    else:
        print("❌ No 'app' attribute found in app module")
        
except Exception as e:
    print(f"❌ Error importing app: {e}")
    import traceback
    traceback.print_exc()

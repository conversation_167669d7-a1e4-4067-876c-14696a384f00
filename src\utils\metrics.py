"""
Metrics calculation utilities for FinBERT sentiment analysis.
"""

import numpy as np
from sklearn.metrics import (
    accuracy_score, precision_recall_fscore_support, 
    confusion_matrix, classification_report, roc_auc_score
)
from sklearn.preprocessing import label_binarize
import torch
from typing import Dict, List, Optional, Tuple
import logging

logger = logging.getLogger(__name__)

def compute_metrics(predictions: np.ndarray, 
                   labels: np.ndarray,
                   label_names: Optional[List[str]] = None) -> Dict:
    """
    Compute comprehensive metrics for classification.
    
    Args:
        predictions: Model predictions
        labels: True labels
        label_names: Optional names for labels
        
    Returns:
        Dictionary with computed metrics
    """
    if label_names is None:
        label_names = ["Negative", "Neutral", "Positive"]
    
    # Basic metrics
    accuracy = accuracy_score(labels, predictions)
    precision, recall, f1, support = precision_recall_fscore_support(
        labels, predictions, average=None, zero_division=0
    )
    
    # Macro and weighted averages
    precision_macro, recall_macro, f1_macro, _ = precision_recall_fscore_support(
        labels, predictions, average='macro', zero_division=0
    )
    precision_weighted, recall_weighted, f1_weighted, _ = precision_recall_fscore_support(
        labels, predictions, average='weighted', zero_division=0
    )
    
    # Confusion matrix
    cm = confusion_matrix(labels, predictions)
    
    # Per-class metrics
    per_class_metrics = {}
    for i, label_name in enumerate(label_names):
        if i < len(precision):
            per_class_metrics[f"{label_name}_precision"] = precision[i]
            per_class_metrics[f"{label_name}_recall"] = recall[i]
            per_class_metrics[f"{label_name}_f1"] = f1[i]
            per_class_metrics[f"{label_name}_support"] = support[i]
    
    metrics = {
        # Overall metrics
        "accuracy": accuracy,
        "precision_macro": precision_macro,
        "recall_macro": recall_macro,
        "f1_macro": f1_macro,
        "precision_weighted": precision_weighted,
        "recall_weighted": recall_weighted,
        "f1_weighted": f1_weighted,
        
        # Per-class metrics
        **per_class_metrics,
        
        # Confusion matrix
        "confusion_matrix": cm.tolist(),
        
        # Classification report
        "classification_report": classification_report(
            labels, predictions, target_names=label_names, output_dict=True
        )
    }
    
    return metrics

def compute_metrics_for_trainer(eval_pred) -> Dict:
    """
    Compute metrics function for Hugging Face Trainer.
    
    Args:
        eval_pred: Tuple of (predictions, labels)
        
    Returns:
        Dictionary with metrics
    """
    predictions, labels = eval_pred
    
    # Get predicted class
    if len(predictions.shape) > 1:
        predictions = np.argmax(predictions, axis=1)
    
    # Compute metrics
    metrics = compute_metrics(predictions, labels)
    
    # Return only the metrics needed for trainer
    return {
        "accuracy": metrics["accuracy"],
        "f1": metrics["f1_macro"],
        "precision": metrics["precision_macro"],
        "recall": metrics["recall_macro"]
    }

def calculate_class_weights(labels: List[int]) -> torch.Tensor:
    """
    Calculate class weights for imbalanced datasets.
    
    Args:
        labels: List of labels
        
    Returns:
        Tensor with class weights
    """
    from collections import Counter
    
    label_counts = Counter(labels)
    total_samples = len(labels)
    num_classes = len(label_counts)
    
    # Calculate weights (inverse frequency)
    weights = []
    for class_id in sorted(label_counts.keys()):
        weight = total_samples / (num_classes * label_counts[class_id])
        weights.append(weight)
    
    return torch.FloatTensor(weights)

def print_metrics_summary(metrics: Dict, 
                         title: str = "Model Performance") -> None:
    """
    Print a formatted summary of metrics.
    
    Args:
        metrics: Dictionary with computed metrics
        title: Title for the summary
    """
    print(f"\n{'='*50}")
    print(f"{title:^50}")
    print(f"{'='*50}")
    
    # Overall metrics
    print(f"\nOverall Performance:")
    print(f"  Accuracy:           {metrics['accuracy']:.4f}")
    print(f"  F1 Score (Macro):   {metrics['f1_macro']:.4f}")
    print(f"  F1 Score (Weighted): {metrics['f1_weighted']:.4f}")
    print(f"  Precision (Macro):  {metrics['precision_macro']:.4f}")
    print(f"  Recall (Macro):     {metrics['recall_macro']:.4f}")
    
    # Per-class metrics
    print(f"\nPer-Class Performance:")
    label_names = ["Negative", "Neutral", "Positive"]
    print(f"{'Class':<10} {'Precision':<10} {'Recall':<10} {'F1-Score':<10} {'Support':<10}")
    print(f"{'-'*60}")
    
    for label_name in label_names:
        precision_key = f"{label_name}_precision"
        recall_key = f"{label_name}_recall"
        f1_key = f"{label_name}_f1"
        support_key = f"{label_name}_support"
        
        if precision_key in metrics:
            precision = metrics[precision_key]
            recall = metrics[recall_key]
            f1 = metrics[f1_key]
            support = int(metrics[support_key])
            
            print(f"{label_name:<10} {precision:<10.4f} {recall:<10.4f} {f1:<10.4f} {support:<10}")
    
    # Confusion matrix
    if 'confusion_matrix' in metrics:
        print(f"\nConfusion Matrix:")
        cm = np.array(metrics['confusion_matrix'])
        print(f"{'':>12}", end="")
        for label in label_names:
            print(f"{label:>10}", end="")
        print()
        
        for i, label in enumerate(label_names):
            print(f"{label:>10}  ", end="")
            for j in range(len(label_names)):
                if i < len(cm) and j < len(cm[i]):
                    print(f"{cm[i][j]:>10}", end="")
                else:
                    print(f"{'0':>10}", end="")
            print()
    
    print(f"{'='*50}\n")

def save_metrics(metrics: Dict, 
                filepath: str,
                additional_info: Optional[Dict] = None) -> None:
    """
    Save metrics to a JSON file.
    
    Args:
        metrics: Dictionary with metrics
        filepath: Path to save the metrics
        additional_info: Additional information to save
    """
    import json
    from datetime import datetime
    
    # Prepare data to save
    save_data = {
        "timestamp": datetime.now().isoformat(),
        "metrics": metrics
    }
    
    if additional_info:
        save_data.update(additional_info)
    
    # Convert numpy arrays to lists for JSON serialization
    def convert_numpy(obj):
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, dict):
            return {key: convert_numpy(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [convert_numpy(item) for item in obj]
        return obj
    
    save_data = convert_numpy(save_data)
    
    # Save to file
    with open(filepath, 'w') as f:
        json.dump(save_data, f, indent=2)
    
    logger.info(f"Metrics saved to {filepath}")

def load_metrics(filepath: str) -> Dict:
    """
    Load metrics from a JSON file.
    
    Args:
        filepath: Path to the metrics file
        
    Returns:
        Dictionary with loaded metrics
    """
    import json
    
    with open(filepath, 'r') as f:
        data = json.load(f)
    
    logger.info(f"Metrics loaded from {filepath}")
    return data

"""
Production configuration for FinBERT sentiment analysis API.

This file contains production-ready settings for deploying the FinBERT
sentiment analyzer in a real website backend environment.
"""

import os
from dataclasses import dataclass
from typing import List, Optional

@dataclass
class ProductionConfig:
    """Production configuration settings."""
    
    # Server settings
    HOST: str = "0.0.0.0"
    PORT: int = 5000
    DEBUG: bool = False
    WORKERS: int = 4
    TIMEOUT: int = 120
    
    # Security settings
    API_KEYS: List[str] = None
    CORS_ORIGINS: List[str] = None
    RATE_LIMIT: str = "100 per hour"
    API_RATE_LIMIT: str = "10 per minute"
    
    # Model settings
    MODEL_PATH: str = "../models/final/finbert_pretrained"
    FALLBACK_MODEL: str = "ProsusAI/finbert"
    MAX_TEXT_LENGTH: int = 512
    BATCH_SIZE: int = 1
    
    # Cache settings
    REDIS_HOST: str = "localhost"
    REDIS_PORT: int = 6379
    REDIS_DB: int = 0
    CACHE_EXPIRATION: int = 1800  # 30 minutes
    
    # Monitoring settings
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "finbert_api.log"
    METRICS_ENABLED: bool = True
    
    # Database settings (for analytics)
    DATABASE_URL: Optional[str] = None
    
    def __post_init__(self):
        """Initialize from environment variables."""
        self.API_KEYS = os.environ.get('FINBERT_API_KEYS', '').split(',') if os.environ.get('FINBERT_API_KEYS') else []
        self.CORS_ORIGINS = os.environ.get('CORS_ORIGINS', '').split(',') if os.environ.get('CORS_ORIGINS') else []
        self.DATABASE_URL = os.environ.get('DATABASE_URL')
        
        # Override with environment variables
        self.HOST = os.environ.get('HOST', self.HOST)
        self.PORT = int(os.environ.get('PORT', self.PORT))
        self.DEBUG = os.environ.get('DEBUG', 'False').lower() == 'true'
        self.WORKERS = int(os.environ.get('WORKERS', self.WORKERS))
        
        self.REDIS_HOST = os.environ.get('REDIS_HOST', self.REDIS_HOST)
        self.REDIS_PORT = int(os.environ.get('REDIS_PORT', self.REDIS_PORT))
        
        self.LOG_LEVEL = os.environ.get('LOG_LEVEL', self.LOG_LEVEL)

# Global configuration instance
config = ProductionConfig()

# Environment-specific configurations
class DevelopmentConfig(ProductionConfig):
    """Development configuration."""
    DEBUG = True
    LOG_LEVEL = "DEBUG"
    RATE_LIMIT = "1000 per hour"
    API_RATE_LIMIT = "100 per minute"

class StagingConfig(ProductionConfig):
    """Staging configuration."""
    DEBUG = False
    LOG_LEVEL = "INFO"
    RATE_LIMIT = "500 per hour"
    API_RATE_LIMIT = "50 per minute"

class ProductionConfig(ProductionConfig):
    """Production configuration."""
    DEBUG = False
    LOG_LEVEL = "WARNING"
    RATE_LIMIT = "100 per hour"
    API_RATE_LIMIT = "10 per minute"

def get_config():
    """Get configuration based on environment."""
    env = os.environ.get('FLASK_ENV', 'production').lower()
    
    if env == 'development':
        return DevelopmentConfig()
    elif env == 'staging':
        return StagingConfig()
    else:
        return ProductionConfig()

# Example environment variables file (.env)
ENV_EXAMPLE = """
# FinBERT API Production Environment Variables

# Server Configuration
HOST=0.0.0.0
PORT=5000
FLASK_ENV=production
DEBUG=False
WORKERS=4

# Security
FINBERT_API_KEYS=your-api-key-1,your-api-key-2,your-api-key-3
CORS_ORIGINS=https://yourdomain.com,https://admin.yourdomain.com

# Redis Cache
REDIS_HOST=localhost
REDIS_PORT=6379

# Database (optional, for analytics)
DATABASE_URL=postgresql://user:password@localhost:5432/finbert_analytics

# Logging
LOG_LEVEL=INFO
"""

# Production deployment commands
DEPLOYMENT_COMMANDS = """
# Production Deployment Commands

# 1. Install dependencies
pip install -r requirements.txt
pip install gunicorn redis flask-limiter flask-cors

# 2. Set environment variables
export FLASK_ENV=production
export FINBERT_API_KEYS=your-secret-api-keys
export CORS_ORIGINS=https://yourdomain.com

# 3. Start with Gunicorn
gunicorn --bind 0.0.0.0:5000 --workers 4 --timeout 120 --access-logfile - --error-logfile - app:app

# 4. Or with systemd service
sudo systemctl start finbert-api
sudo systemctl enable finbert-api

# 5. Behind nginx reverse proxy
# Add to nginx.conf:
# location /api/sentiment {
#     proxy_pass http://localhost:5000;
#     proxy_set_header Host $host;
#     proxy_set_header X-Real-IP $remote_addr;
# }
"""

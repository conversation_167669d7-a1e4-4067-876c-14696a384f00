"""
Simple direct launcher for FinBERT Sentiment Analysis App
"""

if __name__ == "__main__":
    print("🚀 Starting FinBERT Sentiment Analysis Web App...")
    print("🌐 Open your browser and go to: http://localhost:5000")
    print("\n⌨️  Press Ctrl+C to stop the server")
    print("-" * 50)
    
    try:
        # Import the app module and get the Flask app instance
        import app as app_module
        flask_app = app_module.app  # Get the Flask app instance
        flask_app.run(host='0.0.0.0', port=5000, debug=True)
    except KeyboardInterrupt:
        print("\n👋 Application stopped by user")
    except Exception as e:
        print(f"\n❌ Error: {e}")
        print("\nTroubleshooting tips:")
        print("1. Make sure all dependencies are installed: pip install -r requirements.txt")
        print("2. Check if the trained model exists in the correct location")
        print("3. Ensure no other application is using port 5000")

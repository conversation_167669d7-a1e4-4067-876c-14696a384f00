"""
Production-ready FinBERT sentiment analysis API.

This is a production version of the Flask app with security, caching,
monitoring, and error handling for real website integration.
"""

import os
import sys
import logging
import hashlib
import json
import time
from datetime import datetime
from functools import wraps
from typing import Dict, Optional

from flask import Flask, request, jsonify
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
from flask_cors import CORS
import redis
import torch
from transformers import AutoModelForSequenceClassification, AutoTokenizer
import numpy as np

# Add src to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from production_config import get_config

# Initialize configuration
config = get_config()

# Configure logging
logging.basicConfig(
    level=getattr(logging, config.LOG_LEVEL),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(config.LOG_FILE),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'finbert-production-key')

# Rate limiting
limiter = Limiter(
    app,
    key_func=get_remote_address,
    default_limits=[config.RATE_LIMIT]
)

# CORS configuration
if config.CORS_ORIGINS:
    CORS(app, origins=config.CORS_ORIGINS)
else:
    CORS(app)  # Allow all origins in development

# Redis cache (optional)
redis_client = None
try:
    redis_client = redis.Redis(
        host=config.REDIS_HOST, 
        port=config.REDIS_PORT, 
        db=config.REDIS_DB,
        decode_responses=True
    )
    redis_client.ping()
    logger.info("✅ Redis cache connected")
except Exception as e:
    logger.warning(f"⚠️ Redis cache not available: {e}")
    redis_client = None

class ProductionFinBERTAnalyzer:
    """Production-ready FinBERT analyzer with caching and monitoring."""
    
    def __init__(self, model_path: str):
        self.model_path = model_path
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model = None
        self.tokenizer = None
        self.label_mapping = {0: "Negative", 1: "Neutral", 2: "Positive"}
        self.sentiment_colors = {
            "Negative": "#dc3545",
            "Neutral": "#6c757d", 
            "Positive": "#28a745"
        }
        self.analysis_count = 0
        self.load_model()
    
    def load_model(self):
        """Load the fine-tuned FinBERT model with fallback."""
        try:
            if os.path.exists(self.model_path) and os.path.exists(os.path.join(self.model_path, 'model.safetensors')):
                logger.info(f"Loading fine-tuned model from {self.model_path}")
                self.tokenizer = AutoTokenizer.from_pretrained(self.model_path)
                self.model = AutoModelForSequenceClassification.from_pretrained(self.model_path)
                self.model.to(self.device)
                self.model.eval()
                logger.info("✅ Fine-tuned FinBERT model loaded successfully")
                self.model_type = "fine-tuned"
            else:
                logger.warning("⚠️ Fine-tuned model not found. Using base FinBERT.")
                self.tokenizer = AutoTokenizer.from_pretrained(config.FALLBACK_MODEL)
                self.model = AutoModelForSequenceClassification.from_pretrained(config.FALLBACK_MODEL)
                self.model.to(self.device)
                self.model.eval()
                logger.info("✅ Base FinBERT model loaded successfully")
                self.model_type = "base"
                
        except Exception as e:
            logger.error(f"❌ Failed to load model: {e}")
            raise
    
    def analyze_sentiment(self, text: str) -> Dict:
        """Analyze sentiment with caching and monitoring."""
        start_time = time.time()
        
        try:
            # Check cache first
            cache_key = f"sentiment:{hashlib.md5(text.encode()).hexdigest()}"
            if redis_client:
                cached_result = redis_client.get(cache_key)
                if cached_result:
                    logger.info("Cache hit for sentiment analysis")
                    return json.loads(cached_result)
            
            # Tokenize and analyze
            inputs = self.tokenizer(
                text,
                return_tensors="pt",
                truncation=True,
                padding=True,
                max_length=config.MAX_TEXT_LENGTH
            )
            
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            with torch.no_grad():
                outputs = self.model(**inputs)
                logits = outputs.logits
                probabilities = torch.nn.functional.softmax(logits, dim=-1)
            
            probs = probabilities.cpu().numpy()[0]
            predicted_class = np.argmax(probs)
            predicted_label = self.label_mapping[predicted_class]
            confidence = float(probs[predicted_class])
            
            result = {
                'sentiment': predicted_label,
                'confidence': confidence,
                'probabilities': {
                    'Negative': float(probs[0]),
                    'Neutral': float(probs[1]),
                    'Positive': float(probs[2])
                },
                'color': self.sentiment_colors[predicted_label],
                'model_type': self.model_type,
                'analysis_time': time.time() - start_time
            }
            
            # Cache result
            if redis_client:
                redis_client.setex(cache_key, config.CACHE_EXPIRATION, json.dumps(result))
            
            # Update metrics
            self.analysis_count += 1
            
            return result
            
        except Exception as e:
            logger.error(f"Error in sentiment analysis: {e}")
            raise

# Initialize analyzer
MODEL_PATH = os.path.join(os.path.dirname(__file__), '..', 'models', 'final', 'finbert_pretrained')
try:
    analyzer = ProductionFinBERTAnalyzer(MODEL_PATH)
    logger.info("✅ FinBERT Analyzer initialized successfully")
except Exception as e:
    logger.error(f"❌ Failed to initialize analyzer: {e}")
    analyzer = None

# Authentication decorator
def require_api_key(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if config.API_KEYS:
            api_key = request.headers.get('X-API-Key')
            if not api_key or api_key not in config.API_KEYS:
                logger.warning(f"Unauthorized API access attempt from {request.remote_addr}")
                return jsonify({'error': 'Invalid or missing API key'}), 401
        return f(*args, **kwargs)
    return decorated_function

# Routes
@app.route('/health')
def health():
    """Health check endpoint."""
    return jsonify({
        'status': 'healthy' if analyzer else 'unhealthy',
        'model_loaded': analyzer is not None,
        'model_type': analyzer.model_type if analyzer else None,
        'gpu_available': torch.cuda.is_available(),
        'timestamp': datetime.now().isoformat()
    })

@app.route('/api/analyze', methods=['POST'])
@limiter.limit(config.API_RATE_LIMIT)
@require_api_key
def api_analyze():
    """Production API endpoint for sentiment analysis."""
    try:
        if not analyzer:
            return jsonify({'error': 'Model not loaded'}), 500
        
        data = request.get_json()
        if not data or 'text' not in data:
            return jsonify({'error': 'Missing text field in JSON'}), 400
        
        text = data['text'].strip()
        if not text:
            return jsonify({'error': 'Empty text provided'}), 400
        
        if len(text) > config.MAX_TEXT_LENGTH * 4:  # Rough character limit
            return jsonify({'error': 'Text too long'}), 400
        
        # Perform analysis
        result = analyzer.analyze_sentiment(text)
        
        logger.info(f"Sentiment analysis completed: {result['sentiment']} ({result['confidence']:.2f})")
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"API Error: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@app.route('/metrics')
def metrics():
    """Metrics endpoint for monitoring."""
    if not config.METRICS_ENABLED:
        return jsonify({'error': 'Metrics disabled'}), 404
    
    return jsonify({
        'total_analyses': analyzer.analysis_count if analyzer else 0,
        'model_type': analyzer.model_type if analyzer else None,
        'gpu_available': torch.cuda.is_available(),
        'cache_available': redis_client is not None,
        'uptime': time.time() - start_time if 'start_time' in globals() else 0,
        'version': '1.0.0'
    })

@app.errorhandler(429)
def ratelimit_handler(e):
    """Rate limit error handler."""
    logger.warning(f"Rate limit exceeded from {request.remote_addr}")
    return jsonify({'error': 'Rate limit exceeded'}), 429

@app.errorhandler(500)
def internal_error(error):
    """Internal error handler."""
    logger.error(f"Internal server error: {error}")
    return jsonify({'error': 'Internal server error'}), 500

# Global start time for uptime calculation
start_time = time.time()

if __name__ == '__main__':
    logger.info(f"Starting FinBERT API in {config.__class__.__name__} mode")
    logger.info(f"Model type: {analyzer.model_type if analyzer else 'None'}")
    logger.info(f"GPU available: {torch.cuda.is_available()}")
    logger.info(f"Cache available: {redis_client is not None}")
    
    app.run(
        host=config.HOST,
        port=config.PORT,
        debug=config.DEBUG
    )

# FinBERT Evaluation Script for Windows PowerShell
# Usage: .\scripts\evaluate_model.ps1 [model_path] [test_data] [text_column] [label_column]

param(
    [string]$ModelPath = "models\final\finbert_sentiment_final",
    [string]$TestData = "data\raw\data.csv",
    [string]$TextColumn = "cleaned_text",
    [string]$LabelColumn = "labels",
    [string]$OutputDir = "results"
)

Write-Host "=========================================" -ForegroundColor Green
Write-Host "FinBERT Model Evaluation" -ForegroundColor Green
Write-Host "=========================================" -ForegroundColor Green
Write-Host "Model path: $ModelPath"
Write-Host "Test data: $TestData"
Write-Host "Text column: $TextColumn"
Write-Host "Label column: $LabelColumn"
Write-Host "Output directory: $OutputDir"
Write-Host "=========================================" -ForegroundColor Green

# Check if model exists
if (-not (Test-Path $ModelPath)) {
    Write-Host "Error: Model directory '$ModelPath' not found!" -ForegroundColor Red
    Write-Host "Available models:" -ForegroundColor Yellow
    if (Test-Path "models\final") {
        Get-ChildItem "models\final" | ForEach-Object { Write-Host "  $($_.Name)" -ForegroundColor Cyan }
    } else {
        Write-Host "  No models found in models\final\" -ForegroundColor Red
    }
    exit 1
}

# Check if test data exists
if (-not (Test-Path $TestData)) {
    Write-Host "Error: Test data file '$TestData' not found!" -ForegroundColor Red
    exit 1
}

# Create necessary directories
$directories = @("results\metrics", "results\plots", "logs")
foreach ($dir in $directories) {
    if (-not (Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Host "Created directory: $dir" -ForegroundColor Yellow
    }
}

# Activate virtual environment if it exists
if (Test-Path "venv\Scripts\Activate.ps1") {
    Write-Host "Activating virtual environment..." -ForegroundColor Yellow
    & "venv\Scripts\Activate.ps1"
}

# Check if required packages are installed
try {
    python -c "import torch, transformers, pandas" 2>$null
    if ($LASTEXITCODE -ne 0) {
        throw "Required packages not found"
    }
} catch {
    Write-Host "Error: Required packages not installed. Run: pip install -r requirements.txt" -ForegroundColor Red
    exit 1
}

# Run evaluation
Write-Host "Starting model evaluation..." -ForegroundColor Green
Write-Host "Timestamp: $(Get-Date)" -ForegroundColor Cyan

try {
    python src\evaluate.py --model_path $ModelPath --test_data $TestData --text_column $TextColumn --label_column $LabelColumn --output_dir $OutputDir
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "=========================================" -ForegroundColor Green
        Write-Host "Evaluation completed successfully!" -ForegroundColor Green
        Write-Host "Timestamp: $(Get-Date)" -ForegroundColor Cyan
        Write-Host "Check results\metrics\ for detailed metrics" -ForegroundColor Yellow
        Write-Host "Check results\plots\ for visualizations" -ForegroundColor Yellow
        Write-Host "Check results\ for prediction files" -ForegroundColor Yellow
        Write-Host "=========================================" -ForegroundColor Green
    } else {
        Write-Host "Evaluation failed with exit code: $LASTEXITCODE" -ForegroundColor Red
        exit $LASTEXITCODE
    }
} catch {
    Write-Host "Error during evaluation: $_" -ForegroundColor Red
    exit 1
}

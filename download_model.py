#!/usr/bin/env python3
"""
Download script for the fine-tuned FinBERT model.

Since the model files are too large for GitHub (417MB), this script
helps users download the pre-trained model from an external source.
"""

import os
import requests
from pathlib import Path
import zipfile
from tqdm import tqdm

def download_file(url: str, filename: str):
    """Download a file with progress bar."""
    print(f"Downloading {filename}...")
    
    response = requests.get(url, stream=True)
    response.raise_for_status()
    
    total_size = int(response.headers.get('content-length', 0))
    
    with open(filename, 'wb') as file, tqdm(
        desc=filename,
        total=total_size,
        unit='B',
        unit_scale=True,
        unit_divisor=1024,
    ) as pbar:
        for chunk in response.iter_content(chunk_size=8192):
            if chunk:
                file.write(chunk)
                pbar.update(len(chunk))

def extract_model(zip_path: str, extract_to: str):
    """Extract the model files."""
    print(f"Extracting model to {extract_to}...")
    
    with zipfile.ZipFile(zip_path, 'r') as zip_ref:
        zip_ref.extractall(extract_to)
    
    # Remove the zip file after extraction
    os.remove(zip_path)
    print("✅ Model extracted successfully!")

def download_finbert_model():
    """Download the fine-tuned FinBERT model."""
    
    # Create models directory if it doesn't exist
    models_dir = Path("models/final")
    models_dir.mkdir(parents=True, exist_ok=True)
    
    # Check if model already exists
    model_path = models_dir / "finbert_pretrained"
    if model_path.exists() and (model_path / "model.safetensors").exists():
        print("✅ Model already exists!")
        return
    
    print("🚀 Downloading Fine-tuned FinBERT Model...")
    print("📊 Model Performance: 83.8% accuracy on financial sentiment")
    print("💾 Size: ~417MB")
    print()
    
    # TODO: Replace with actual download URL
    # Options for hosting the model:
    # 1. Google Drive public link
    # 2. Hugging Face Hub
    # 3. Dropbox public link
    # 4. GitHub Release assets
    
    model_url = "https://example.com/finbert_model.zip"  # Replace with actual URL
    
    try:
        # Download the model
        zip_filename = "finbert_model.zip"
        download_file(model_url, zip_filename)
        
        # Extract the model
        extract_model(zip_filename, str(models_dir))
        
        print("🎉 Model download completed!")
        print("🚀 You can now run the application:")
        print("   cd deployment")
        print("   python app.py")
        
    except requests.exceptions.RequestException as e:
        print(f"❌ Download failed: {e}")
        print()
        print("📋 Manual Download Instructions:")
        print("1. Download the model from: [PROVIDE MANUAL LINK]")
        print("2. Extract to: models/final/finbert_pretrained/")
        print("3. Ensure these files exist:")
        print("   - models/final/finbert_pretrained/model.safetensors")
        print("   - models/final/finbert_pretrained/config.json")
        print("   - models/final/finbert_pretrained/tokenizer.json")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    download_finbert_model()

import requests
import json

# Test the API
try:
    print('🧪 Testing FinBERT API...')
    
    # Test health endpoint
    response = requests.get('http://localhost:5000/health')
    if response.status_code == 200:
        print('✅ Health check passed')
        health_data = response.json()
        print(f'   Status: {health_data["status"]}')
        print(f'   Model loaded: {health_data["model_loaded"]}')
    
    # Test sentiment analysis
    test_text = 'The company reported record quarterly earnings with revenue growth of 25% year-over-year.'
    response = requests.post(
        'http://localhost:5000/api/analyze',
        json={'text': test_text}
    )
    
    if response.status_code == 200:
        data = response.json()
        print('✅ Sentiment analysis test passed')
        print(f'   Text: {test_text[:50]}...')
        print(f'   Sentiment: {data["sentiment"]}')
        print(f'   Confidence: {data["confidence"]:.1%}')
        print(f'   Summary: {data["summary"][:100]}...')
        print('🎉 All tests passed!')
    else:
        print(f'❌ API test failed: {response.status_code}')
        print(f'   Error: {response.text}')
        
except Exception as e:
    print(f'❌ Test failed: {e}')

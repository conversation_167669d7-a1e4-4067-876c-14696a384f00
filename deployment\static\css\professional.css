/* Professional Financial Business Theme - Black & White */

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

/* Additional professional styling */
.professional-header {
    background: linear-gradient(135deg, #000000 0%, #1a1a1a 100%);
    position: relative;
    overflow: hidden;
}

.professional-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23333" stroke-width="0.5" opacity="0.3"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.1;
}

.logo-container {
    position: relative;
    z-index: 2;
}

.professional-badge {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    color: #ffffff;
    margin-left: 1rem;
}

.analysis-card {
    background: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
}

.analysis-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    border-color: #000000;
}

.metric-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #f8f9fa;
}

.metric-row:last-child {
    border-bottom: none;
}

.metric-label {
    font-weight: 600;
    color: #495057;
    font-size: 0.9rem;
}

.metric-value {
    font-weight: 700;
    color: #000000;
    font-size: 1rem;
}

.professional-button {
    background: #000000;
    color: #ffffff;
    border: 2px solid #000000;
    padding: 0.875rem 2rem;
    font-weight: 600;
    font-size: 0.95rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-radius: 4px;
    transition: all 0.2s ease;
    cursor: pointer;
}

.professional-button:hover {
    background: #1a1a1a;
    border-color: #1a1a1a;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.professional-button:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.professional-input {
    border: 2px solid #dee2e6;
    border-radius: 4px;
    padding: 0.875rem 1rem;
    font-size: 1rem;
    font-family: 'Inter', sans-serif;
    transition: all 0.2s ease;
    background: #ffffff;
    color: #000000;
}

.professional-input:focus {
    border-color: #000000;
    box-shadow: 0 0 0 0.2rem rgba(0, 0, 0, 0.1);
    outline: none;
}

.professional-input::placeholder {
    color: #6c757d;
    font-weight: 400;
}

.sentiment-indicator {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    font-weight: 600;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.sentiment-indicator.positive {
    background: rgba(40, 167, 69, 0.1);
    color: #28a745;
    border: 1px solid #28a745;
}

.sentiment-indicator.negative {
    background: rgba(220, 53, 69, 0.1);
    color: #dc3545;
    border: 1px solid #dc3545;
}

.sentiment-indicator.neutral {
    background: rgba(108, 117, 125, 0.1);
    color: #6c757d;
    border: 1px solid #6c757d;
}

.professional-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
}

.professional-table th,
.professional-table td {
    padding: 0.875rem 1rem;
    text-align: left;
    border-bottom: 1px solid #dee2e6;
}

.professional-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #495057;
    text-transform: uppercase;
    font-size: 0.8rem;
    letter-spacing: 0.5px;
}

.professional-table td {
    color: #000000;
    font-weight: 500;
}

.professional-table tr:hover {
    background: #f8f9fa;
}

.loading-professional {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem;
    background: #ffffff;
    border: 2px solid #dee2e6;
    border-radius: 4px;
}

.loading-spinner-professional {
    width: 32px;
    height: 32px;
    border: 3px solid #dee2e6;
    border-top: 3px solid #000000;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

.loading-text-professional {
    color: #6c757d;
    font-weight: 500;
    font-size: 0.95rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive design */
@media (max-width: 768px) {
    .logo-container {
        flex-direction: column;
        text-align: center;
    }
    
    .logo {
        margin-right: 0;
        margin-bottom: 1rem;
    }
    
    .professional-badge {
        margin-left: 0;
        margin-top: 0.5rem;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
}

/* Print styles */
@media print {
    .header {
        background: #000000 !important;
        color: #ffffff !important;
    }
    
    .main-container {
        box-shadow: none;
        border: 1px solid #000000;
    }
    
    .professional-button {
        display: none;
    }
}

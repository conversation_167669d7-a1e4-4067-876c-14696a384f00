@echo off
echo ================================================================
echo FinBERT Sentiment Analysis Web Application Launcher
echo ================================================================
echo.

cd /d "%~dp0"

echo 🔍 Checking Python installation...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed or not in PATH
    echo Please install Python 3.8+ and add it to your PATH
    pause
    exit /b 1
)

echo ✅ Python found
python --version

echo.
echo 📦 Installing/checking dependencies...
python -m pip install -r requirements.txt
if errorlevel 1 (
    echo ❌ Failed to install dependencies
    pause
    exit /b 1
)

echo.
echo 🚀 Starting FinBERT Sentiment Analysis App...
echo 🌐 Open your browser and go to: http://localhost:5000
echo.
echo Press Ctrl+C to stop the server
echo.

python run_app.py

pause

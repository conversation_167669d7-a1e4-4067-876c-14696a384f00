# FinBERT Project Cleanup Script
# This script removes redundant files to streamline for Hugging Face Model Directory approach
# TOTAL SPACE SAVINGS: ~6.85 GB

Write-Host "🧹 Starting FinBERT Project Cleanup..." -ForegroundColor Green
Write-Host "This will save approximately 6.85 GB of disk space" -ForegroundColor Yellow

# 1. Remove intermediate training checkpoints (~1.5 GB)
Write-Host "`n1. Removing training checkpoints..." -ForegroundColor Cyan
if (Test-Path "models\checkpoints") {
    Remove-Item "models\checkpoints" -Recurse -Force
    Write-Host "   ✅ Deleted models/checkpoints/ (~1.5 GB)" -ForegroundColor Green
}

# 2. Remove duplicate model files from results (~4.2 GB)
Write-Host "`n2. Removing duplicate model files from results..." -ForegroundColor Cyan
if (Test-Path "results\finbert_results\checkpoint-190") {
    Remove-Item "results\finbert_results\checkpoint-190" -Recurse -Force
    Write-Host "   ✅ Deleted results/finbert_results/checkpoint-190/" -ForegroundColor Green
}
if (Test-Path "results\finbert_results\checkpoint-285") {
    Remove-Item "results\finbert_results\checkpoint-285" -Recurse -Force  
    Write-Host "   ✅ Deleted results/finbert_results/checkpoint-285/" -ForegroundColor Green
}
if (Test-Path "results\finbert_results\final_model") {
    Remove-Item "results\finbert_results\final_model" -Recurse -Force
    Write-Host "   ✅ Deleted results/finbert_results/final_model/" -ForegroundColor Green
}
if (Test-Path "results\finbert_results\model_export") {
    Remove-Item "results\finbert_results\model_export" -Recurse -Force
    Write-Host "   ✅ Deleted results/finbert_results/model_export/ (pickle files)" -ForegroundColor Green
}

# 3. Remove legacy model storage (~1.2 GB)
Write-Host "`n3. Removing legacy model storage..." -ForegroundColor Cyan
if (Test-Path "models\final\finbert_pretrained") {
    Remove-Item "models\final\finbert_pretrained" -Recurse -Force
    Write-Host "   ✅ Deleted models/final/finbert_pretrained/ (~1.2 GB)" -ForegroundColor Green
}

# 4. Remove redundant processed data
Write-Host "`n4. Removing redundant processed data..." -ForegroundColor Cyan
if (Test-Path "data\processed\exported_csv_data_original_splits") {
    Remove-Item "data\processed\exported_csv_data_original_splits" -Recurse -Force
    Write-Host "   ✅ Deleted data/processed/exported_csv_data_original_splits/" -ForegroundColor Green
}
if (Test-Path "data\processed\preprocessed_datasets_original_splits") {
    Remove-Item "data\processed\preprocessed_datasets_original_splits" -Recurse -Force
    Write-Host "   ✅ Deleted data/processed/preprocessed_datasets_original_splits/" -ForegroundColor Green
}

# 5. Remove development/experimental notebooks
Write-Host "`n5. Removing development notebooks..." -ForegroundColor Cyan
$notebooksToDelete = @(
    "notebooks\NLP.ipynb",
    "notebooks\NLP2.ipynb", 
    "notebooks\Quick_Start_Guide.ipynb",
    "notebooks\Step2_Prepare_Dataset.ipynb",
    "notebooks\Step3_Data_Preprocessing_Original_Splits.ipynb"
)

foreach ($notebook in $notebooksToDelete) {
    if (Test-Path $notebook) {
        Remove-Item $notebook -Force
        Write-Host "   ✅ Deleted $notebook" -ForegroundColor Green
    }
}

# 6. Remove empty directories
Write-Host "`n6. Removing empty directories..." -ForegroundColor Cyan
$dirsToCheck = @("logs", "results\metrics", "results\plots", "models\checkpoints", "models\final")
foreach ($dir in $dirsToCheck) {
    if ((Test-Path $dir) -and ((Get-ChildItem $dir -Force | Measure-Object).Count -eq 0)) {
        Remove-Item $dir -Force
        Write-Host "   ✅ Deleted empty directory: $dir" -ForegroundColor Green
    }
}

# 7. Clean up any remaining empty folders
Write-Host "`n7. Final cleanup..." -ForegroundColor Cyan
Get-ChildItem -Path . -Recurse -Directory | Where-Object { (Get-ChildItem $_.FullName -Force | Measure-Object).Count -eq 0 } | Remove-Item -Force
Write-Host "   ✅ Removed any remaining empty directories" -ForegroundColor Green

Write-Host "`n🎉 Cleanup completed successfully!" -ForegroundColor Green
Write-Host "💾 Estimated space saved: ~6.85 GB" -ForegroundColor Yellow
Write-Host "`n📁 Remaining project structure optimized for Hugging Face Model Directory approach" -ForegroundColor Cyan

# Show final project size
Write-Host "`n📊 Final project statistics:" -ForegroundColor Cyan
$finalStats = Get-ChildItem -Recurse | Where-Object { $_.PSIsContainer -eq $false } | Measure-Object -Property Length -Sum
$finalSize = [math]::Round($finalStats.Sum/1MB,2)
$finalCount = $finalStats.Count
Write-Host "   Total files: $finalCount" -ForegroundColor White
Write-Host "   Total size: $finalSize MB" -ForegroundColor White

// FinBERT Sentiment Analyzer Frontend JavaScript

class SentimentAnalyzer {
    constructor() {
        this.initializeEventListeners();
        this.setupScrollAnimations();
    }

    initializeEventListeners() {
        // Form submission
        document.getElementById('sentimentForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.analyzeSentiment();
        });

        // Clear button
        document.getElementById('clearBtn').addEventListener('click', () => {
            this.clearForm();
        });

        // Enter key in textarea (Ctrl+Enter to submit)
        document.getElementById('textInput').addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 'Enter') {
                e.preventDefault();
                this.analyzeSentiment();
            }
        });

        // Character counter
        document.getElementById('textInput').addEventListener('input', (e) => {
            this.updateCharacterCount(e.target.value);
        });
    }

    setupScrollAnimations() {
        // Add scroll animations to elements
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                }
            });
        }, observerOptions);

        // Observe elements for scroll animations
        document.querySelectorAll('.card, .feature-card').forEach(el => {
            el.classList.add('scroll-animation');
            observer.observe(el);
        });
    }

    updateCharacterCount(text) {
        const wordCount = text.trim().split(/\s+/).filter(word => word.length > 0).length;
        const charCount = text.length;
        
        // You can add a character counter display here if needed
        console.log(`Characters: ${charCount}, Words: ${wordCount}`);
    }

    async analyzeSentiment() {
        const textInput = document.getElementById('textInput');
        const text = textInput.value.trim();

        // Validation
        if (!text) {
            this.showError('Please enter some text to analyze.');
            return;
        }

        if (text.length < 10) {
            this.showError('Please enter at least 10 characters for meaningful analysis.');
            return;
        }

        try {
            // Show loading state
            this.showLoading();
            this.hideError();
            this.hideResults();

            // Make API request
            const response = await fetch('/analyze', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ text: text })
            });

            const result = await response.json();

            if (!response.ok) {
                throw new Error(result.error || 'Analysis failed');
            }

            // Display results
            this.displayResults(result);

        } catch (error) {
            console.error('Analysis error:', error);
            this.showError(error.message || 'An error occurred during analysis. Please try again.');
        } finally {
            this.hideLoading();
        }
    }

    displayResults(result) {
        // Update sentiment label
        const sentimentLabel = document.getElementById('sentimentLabel');
        const sentiment = result.sentiment.toLowerCase();
        
        sentimentLabel.textContent = result.sentiment.toUpperCase();
        sentimentLabel.className = `badge fs-5 me-3 sentiment-${sentiment}`;

        // Update confidence score
        const confidenceScore = document.getElementById('confidenceScore');
        const confidence = (result.confidence * 100).toFixed(1);
        confidenceScore.innerHTML = `<span class="confidence-score">${confidence}% Confident</span>`;

        // Update probability bars
        this.updateProbabilityBars(result.all_probabilities);

        // Update summary
        document.getElementById('summaryText').textContent = result.summary;

        // Update explanation
        document.getElementById('explanationText').textContent = result.explanation;

        // Update key phrases
        this.updateKeyPhrases(result.key_phrases);

        // Update analysis details
        document.getElementById('textLength').textContent = result.text_length;
        document.getElementById('analysisTime').textContent = this.formatTimestamp(result.analysis_timestamp);

        // Show results with animation
        this.showResults();
    }

    updateProbabilityBars(probabilities) {
        const probabilityBars = document.getElementById('probabilityBars');
        probabilityBars.innerHTML = '';

        // Define colors for each sentiment
        const colors = {
            'positive': 'bg-success',
            'negative': 'bg-danger',
            'neutral': 'bg-secondary'
        };

        // Sort probabilities by value (highest first)
        const sortedProbs = Object.entries(probabilities)
            .sort(([,a], [,b]) => b - a);

        sortedProbs.forEach(([sentiment, probability]) => {
            const percentage = (probability * 100).toFixed(1);
            const colorClass = colors[sentiment] || 'bg-secondary';

            const barHtml = `
                <div class="probability-bar">
                    <div class="probability-label d-flex justify-content-between">
                        <span class="text-capitalize">${sentiment}</span>
                        <span>${percentage}%</span>
                    </div>
                    <div class="progress">
                        <div class="progress-bar ${colorClass}" 
                             role="progressbar" 
                             style="width: ${percentage}%"
                             aria-valuenow="${percentage}" 
                             aria-valuemin="0" 
                             aria-valuemax="100">
                        </div>
                    </div>
                </div>
            `;
            probabilityBars.innerHTML += barHtml;
        });
    }

    updateKeyPhrases(keyPhrases) {
        const keyPhrasesContainer = document.getElementById('keyPhrases');
        const keyPhrasesSection = document.getElementById('keyPhrasesSection');

        if (!keyPhrases || keyPhrases.length === 0) {
            keyPhrasesSection.style.display = 'none';
            return;
        }

        keyPhrasesSection.style.display = 'block';
        keyPhrasesContainer.innerHTML = '';

        keyPhrases.forEach(phrase => {
            const phraseElement = document.createElement('span');
            phraseElement.className = 'key-phrase interactive-element';
            phraseElement.textContent = phrase;
            phraseElement.title = `Key indicator: "${phrase}"`;
            keyPhrasesContainer.appendChild(phraseElement);
        });
    }

    formatTimestamp(timestamp) {
        try {
            const date = new Date(timestamp);
            return date.toLocaleString();
        } catch (error) {
            return 'Just now';
        }
    }

    showLoading() {
        document.getElementById('loadingIndicator').style.display = 'block';
        document.getElementById('analyzeBtn').disabled = true;
        document.getElementById('analyzeBtn').innerHTML = 
            '<i class="fas fa-spinner fa-spin me-2"></i>Analyzing...';
    }

    hideLoading() {
        document.getElementById('loadingIndicator').style.display = 'none';
        document.getElementById('analyzeBtn').disabled = false;
        document.getElementById('analyzeBtn').innerHTML = 
            '<i class="fas fa-search me-2"></i>Analyze Sentiment';
    }

    showResults() {
        const resultsSection = document.getElementById('resultsSection');
        resultsSection.style.display = 'block';
        resultsSection.classList.add('fade-in-up');
        
        // Scroll to results
        resultsSection.scrollIntoView({ 
            behavior: 'smooth', 
            block: 'start' 
        });
    }

    hideResults() {
        document.getElementById('resultsSection').style.display = 'none';
    }

    showError(message) {
        const errorSection = document.getElementById('errorSection');
        const errorMessage = document.getElementById('errorMessage');
        
        errorMessage.textContent = message;
        errorSection.style.display = 'block';
        errorSection.classList.add('fade-in-up');
        
        // Scroll to error
        errorSection.scrollIntoView({ 
            behavior: 'smooth', 
            block: 'start' 
        });
    }

    hideError() {
        document.getElementById('errorSection').style.display = 'none';
    }

    clearForm() {
        document.getElementById('textInput').value = '';
        this.hideResults();
        this.hideError();
        document.getElementById('textInput').focus();
    }

    // Utility method to check API health
    async checkApiHealth() {
        try {
            const response = await fetch('/health');
            const health = await response.json();
            console.log('API Health:', health);
            return health.status === 'healthy';
        } catch (error) {
            console.error('Health check failed:', error);
            return false;
        }
    }

    // Method to load example text
    loadExample(exampleText) {
        document.getElementById('textInput').value = exampleText;
        document.getElementById('textInput').scrollIntoView({ 
            behavior: 'smooth' 
        });
    }
}

// Example texts for demonstration
const exampleTexts = {
    positive: "Apple Inc. reported record quarterly earnings of $89.5 billion, surpassing analyst expectations by 12%. The company's iPhone sales grew 25% year-over-year, driven by strong demand for the new iPhone models. CEO Tim Cook expressed optimism about the company's future prospects, highlighting significant growth in services revenue and successful expansion into emerging markets.",
    
    negative: "Tesla shares plummeted 15% in after-hours trading following disappointing quarterly results. The electric vehicle manufacturer missed revenue expectations by $2.3 billion and announced significant production delays at its new facilities. CEO Elon Musk warned of challenging market conditions ahead, citing supply chain disruptions and increased competition from traditional automakers.",
    
    neutral: "Microsoft Corporation will release its quarterly earnings report next Tuesday, according to a company statement. Analysts forecast revenue of approximately $52.7 billion for the quarter, representing modest growth compared to the same period last year. The technology giant is expected to provide updates on its cloud computing and productivity software divisions during the earnings call."
};

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    const analyzer = new SentimentAnalyzer();
    
    // Check API health on startup
    analyzer.checkApiHealth().then(isHealthy => {
        if (!isHealthy) {
            console.warn('API health check failed - some features may not work');
        }
    });

    // Add example text buttons (optional feature)
    const addExampleButtons = () => {
        const exampleContainer = document.createElement('div');
        exampleContainer.className = 'mt-3 text-center';
        exampleContainer.innerHTML = `
            <small class="text-muted d-block mb-2">Or try these examples:</small>
            <button type="button" class="btn btn-outline-success btn-sm me-2" onclick="loadExample('positive')">
                <i class="fas fa-thumbs-up me-1"></i>Positive Example
            </button>
            <button type="button" class="btn btn-outline-danger btn-sm me-2" onclick="loadExample('negative')">
                <i class="fas fa-thumbs-down me-1"></i>Negative Example
            </button>
            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="loadExample('neutral')">
                <i class="fas fa-minus me-1"></i>Neutral Example
            </button>
        `;
        
        const formCard = document.querySelector('#sentimentForm').closest('.card-body');
        formCard.appendChild(exampleContainer);
    };

    // Add example buttons
    addExampleButtons();

    // Global function to load examples
    window.loadExample = (type) => {
        if (exampleTexts[type]) {
            analyzer.loadExample(exampleTexts[type]);
        }
    };

    console.log('FinBERT Sentiment Analyzer initialized successfully');
});

// Add smooth scrolling for navigation links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Add keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Escape key to clear form
    if (e.key === 'Escape') {
        document.getElementById('textInput').blur();
    }
    
    // Ctrl+K to focus on text input
    if (e.ctrlKey && e.key === 'k') {
        e.preventDefault();
        document.getElementById('textInput').focus();
    }
});

// Performance monitoring
const startTime = performance.now();
window.addEventListener('load', function() {
    const loadTime = performance.now() - startTime;
    console.log(`Page loaded in ${loadTime.toFixed(2)} milliseconds`);
});

{"cells": [{"cell_type": "markdown", "id": "7d2e7f3f", "metadata": {}, "source": ["# **FinBERT Complete Training Pipeline for Google Colab**\n", "## **Financial Sentiment Analysis - Data Preprocessing + Model Training**\n", "\n", "This notebook provides a complete end-to-end pipeline for FinBERT fine-tuning on financial sentiment analysis, optimized for Google Colab.\n", "\n", "### **📋 Pipeline Overview:**\n", "1. **🔧 Environment Setup**: Install dependencies and configure GPU\n", "2. **📊 Data Preprocessing**: Clean and prepare `data.csv` for training\n", "3. **🤖 Model Configuration**: Set up FinBERT for fine-tuning\n", "4. **🏋️ Training Process**: Fine-tune model with monitoring\n", "5. **📈 Evaluation**: Test model performance\n", "6. **💾 Model Saving**: Save trained model\n", "\n", "### **📁 Expected Data Format:**\n", "- `data.csv` with columns:\n", "  - `Sentence`: Financial text/news for sentiment analysis\n", "  - `Sentiment`: Labels (positive, negative, neutral)\n", "\n", "### **🎯 Outcome:**\n", "- Fine-tuned FinBERT model for financial sentiment classification\n", "- Comprehensive evaluation metrics and performance analysis\n", "- Production-ready model files\n", "\n", "**⚡ Optimized for Google Colab with GPU acceleration**"]}, {"cell_type": "markdown", "id": "ca489830", "metadata": {}, "source": ["## **1. Environment Setup & Installation**\n", "\n", "### **🔧 Install Dependencies and Configure Environment**"]}, {"cell_type": "code", "execution_count": null, "id": "fc97713f", "metadata": {}, "outputs": [], "source": ["# Check GPU availability and setup\n", "import torch\n", "import os\n", "\n", "print(\"🔍 Environment Check:\")\n", "print(f\"PyTorch version: {torch.__version__}\")\n", "print(f\"CUDA available: {torch.cuda.is_available()}\")\n", "\n", "if torch.cuda.is_available():\n", "    print(f\"CUDA version: {torch.version.cuda}\")\n", "    print(f\"GPU device: {torch.cuda.get_device_name(0)}\")\n", "    print(f\"GPU memory: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB\")\n", "    device = torch.device(\"cuda\")\n", "    print(\"✅ GPU acceleration enabled!\")\n", "else:\n", "    device = torch.device(\"cpu\")\n", "    print(\"⚠️ GPU not available, using CPU (training will be slower)\")\n", "\n", "# Set memory optimization for large models\n", "if torch.cuda.is_available():\n", "    torch.cuda.empty_cache()\n", "    print(\"🧹 GPU cache cleared\")\n", "\n", "print(f\"\\n🎯 Using device: {device}\")"]}, {"cell_type": "code", "execution_count": null, "id": "039df30d", "metadata": {}, "outputs": [], "source": ["# Install required packages\n", "!pip install transformers datasets accelerate evaluate scikit-learn\n", "!pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118\n", "!pip install matp<PERSON><PERSON>b seaborn plotly wordcloud\n", "\n", "print(\"✅ All packages installed successfully!\")"]}, {"cell_type": "code", "execution_count": null, "id": "d4c1ad24", "metadata": {}, "outputs": [], "source": ["# Import all required libraries\n", "import pandas as pd\n", "import numpy as np\n", "import re\n", "import os\n", "import json\n", "import pickle\n", "from collections import Counter\n", "from typing import Dict, List, Tuple\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Machine Learning and NLP libraries\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.metrics import classification_report, confusion_matrix, accuracy_score\n", "from sklearn.utils import shuffle\n", "\n", "# HuggingFace Transformers and Datasets\n", "from transformers import (\n", "    AutoTokenizer, \n", "    AutoConfig, \n", "    AutoModelForSequenceClassification,\n", "    TrainingArguments,\n", "    Trainer,\n", "    EarlyStopping<PERSON><PERSON><PERSON>,\n", "    DataCollatorWithPadding\n", ")\n", "from datasets import Dataset, DatasetDict, load_metric\n", "import torch\n", "from torch.utils.data import DataLoader\n", "import torch.nn.functional as F\n", "\n", "# Visualization libraries\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import plotly.express as px\n", "import plotly.graph_objects as go\n", "\n", "# Text preprocessing\n", "from html import unescape\n", "from unicodedata import normalize\n", "\n", "# Training utilities\n", "from datetime import datetime\n", "import time\n", "from tqdm.auto import tqdm\n", "\n", "# Set style for better visualizations\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "\n", "print(\"✅ All libraries imported successfully!\")\n", "print(f\"🔥 Ready for FinBERT training on {device}!\")"]}, {"cell_type": "markdown", "id": "ea58b452", "metadata": {}, "source": ["## **2. Data Upload & Exploration**\n", "\n", "### **📁 Upload and Load Financial Sentiment Data**"]}, {"cell_type": "code", "execution_count": null, "id": "e8b1d028", "metadata": {}, "outputs": [], "source": ["# Upload data file in Google Colab\n", "from google.colab import files\n", "import io\n", "\n", "print(\"📂 Please upload your data.csv file:\")\n", "print(\"Expected format: CSV with 'Sentence' and 'Sentiment' columns\")\n", "\n", "# Upload file\n", "uploaded = files.upload()\n", "\n", "# Get the uploaded file\n", "filename = list(uploaded.keys())[0]\n", "print(f\"\\n✅ File uploaded: {filename}\")\n", "\n", "# Load the CSV data\n", "try:\n", "    df = pd.read_csv(io.BytesIO(uploaded[filename]), encoding='utf-8')\n", "    print(f\"✅ Data loaded successfully!\")\n", "except UnicodeDecodeError:\n", "    print(\"⚠️ UTF-8 encoding failed, trying with latin-1...\")\n", "    df = pd.read_csv(io.BytesIO(uploaded[filename]), encoding='latin-1')\n", "    print(f\"✅ Data loaded with latin-1 encoding!\")\n", "\n", "# Basic dataset information\n", "print(f\"\\n📈 Dataset Overview:\")\n", "print(f\"Shape: {df.shape}\")\n", "print(f\"Columns: {list(df.columns)}\")\n", "print(f\"Memory usage: {df.memory_usage(deep=True).sum() / 1024**2:.2f} MB\")\n", "\n", "# Display first few rows\n", "print(f\"\\n📝 First 5 rows:\")\n", "display(df.head())\n", "\n", "print(f\"\\n📊 Data Types:\")\n", "print(df.dtypes)"]}, {"cell_type": "code", "execution_count": null, "id": "0f9f7ea2", "metadata": {}, "outputs": [], "source": ["# Data quality analysis\n", "print(\"🔍 Data Quality Analysis:\")\n", "\n", "# Check for missing values\n", "print(f\"\\n❓ Missing values:\")\n", "missing_data = df.isnull().sum()\n", "for col, missing_count in missing_data.items():\n", "    if missing_count > 0:\n", "        print(f\"  {col}: {missing_count} ({missing_count/len(df)*100:.2f}%)\")\n", "    else:\n", "        print(f\"  {col}: No missing values ✅\")\n", "\n", "# Check for empty strings\n", "print(f\"\\n📝 Empty strings:\")\n", "for col in df.columns:\n", "    if df[col].dtype == 'object':\n", "        empty_count = (df[col].astype(str).str.strip() == '').sum()\n", "        if empty_count > 0:\n", "            print(f\"  {col}: {empty_count} empty strings\")\n", "        else:\n", "            print(f\"  {col}: No empty strings ✅\")\n", "\n", "# Check for duplicates\n", "duplicate_count = df.duplicated().sum()\n", "print(f\"\\n🔄 Duplicate rows: {duplicate_count}\")\n", "\n", "# Sentiment label distribution\n", "print(f\"\\n📊 Sentiment Distribution:\")\n", "sentiment_counts = df['Sentiment'].value_counts()\n", "print(sentiment_counts)\n", "\n", "print(f\"\\nPercentages:\")\n", "sentiment_percentages = df['Sentiment'].value_counts(normalize=True) * 100\n", "for sentiment, percentage in sentiment_percentages.items():\n", "    print(f\"  {sentiment}: {percentage:.2f}%\")\n", "\n", "# Visualize sentiment distribution\n", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))\n", "\n", "# Bar plot\n", "sentiment_counts.plot(kind='bar', ax=ax1, color=['#d62728', '#ff7f0e', '#2ca02c'])\n", "ax1.set_title('Sentiment Distribution (Count)', fontsize=14, fontweight='bold')\n", "ax1.set_xlabel('Sentiment')\n", "ax1.set_ylabel('Count')\n", "ax1.tick_params(axis='x', rotation=45)\n", "\n", "# Pie chart\n", "ax2.pie(sentiment_counts.values, labels=sentiment_counts.index, autopct='%1.1f%%', \n", "        colors=['#d62728', '#ff7f0e', '#2ca02c'], startangle=90)\n", "ax2.set_title('Sentiment Distribution (Percentage)', fontsize=14, fontweight='bold')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(f\"\\n✅ Data exploration completed!\")"]}, {"cell_type": "markdown", "id": "f37396a0", "metadata": {}, "source": ["## **3. Data Cleaning & Preprocessing**\n", "\n", "### **🧹 Clean and Prepare Text Data for FinBERT Training**"]}, {"cell_type": "code", "execution_count": null, "id": "2926ab59", "metadata": {}, "outputs": [], "source": ["# Text cleaning functions for financial data\n", "def clean_financial_text(text):\n", "    \"\"\"\n", "    Comprehensive text cleaning function optimized for financial text data\n", "    \"\"\"\n", "    if pd.isna(text) or text is None:\n", "        return \"\"\n", "    \n", "    # Convert to string if not already\n", "    text = str(text)\n", "    \n", "    # Decode HTML entities\n", "    text = unescape(text)\n", "    \n", "    # Remove HTML tags\n", "    text = re.sub(r'<[^>]+>', '', text)\n", "    \n", "    # Normalize unicode characters\n", "    text = normalize('NFKD', text)\n", "    \n", "    # Remove control characters but keep basic punctuation\n", "    text = re.sub(r'[\\x00-\\x08\\x0B\\x0C\\x0E-\\x1F\\x7F-\\x9F]', ' ', text)\n", "    \n", "    # Fix common encoding issues\n", "    text = text.replace('â€™', \"'\")\n", "    text = text.replace('â€œ', '\"')\n", "    text = text.replace('â€�', '\"')\n", "    text = text.replace('â€\"', '-')\n", "    text = text.replace('â€\"', '--')\n", "    \n", "    # Clean up multiple spaces, tabs, and newlines\n", "    text = re.sub(r'\\s+', ' ', text)\n", "    \n", "    # Remove leading/trailing whitespace\n", "    text = text.strip()\n", "    \n", "    return text\n", "\n", "def validate_financial_text(text, min_length=10):\n", "    \"\"\"\n", "    Validate if text meets requirements for financial sentiment analysis\n", "    \"\"\"\n", "    if pd.isna(text) or text is None:\n", "        return False\n", "    \n", "    text = str(text).strip()\n", "    \n", "    # Check minimum length\n", "    if len(text) < min_length:\n", "        return False\n", "    \n", "    # Check if text contains at least some alphabetic characters\n", "    if not re.search(r'[a-zA-Z]', text):\n", "        return False\n", "    \n", "    # Check for meaningful content (not just numbers/symbols)\n", "    alpha_ratio = len(re.findall(r'[a-zA-Z]', text)) / len(text)\n", "    if alpha_ratio < 0.3:  # At least 30% alphabetic characters\n", "        return False\n", "    \n", "    return True\n", "\n", "print(\"✅ Text cleaning functions defined!\")\n", "print(\"📋 Functions available:\")\n", "print(\"  - clean_financial_text(): Comprehensive text cleaning\")\n", "print(\"  - validate_financial_text(): Text quality validation\")"]}, {"cell_type": "code", "execution_count": null, "id": "8c9eb302", "metadata": {}, "outputs": [], "source": ["# Apply comprehensive data cleaning\n", "print(\"🧹 Starting data cleaning process...\")\n", "df_clean = df.copy()\n", "initial_rows = len(df_clean)\n", "\n", "print(f\"Initial dataset size: {initial_rows:,} rows\")\n", "\n", "# Clean the Sentence column\n", "print(\"\\n🔧 Cleaning text data...\")\n", "df_clean['Sentence_cleaned'] = df_clean['Sentence'].apply(clean_financial_text)\n", "\n", "# Remove rows with invalid text\n", "print(\"🔍 Filtering invalid entries...\")\n", "valid_mask = df_clean['Sentence_cleaned'].apply(validate_financial_text)\n", "invalid_count = (~valid_mask).sum()\n", "df_clean = df_clean[valid_mask].copy()\n", "\n", "print(f\"Removed {invalid_count:,} invalid entries ({invalid_count/initial_rows*100:.1f}%)\")\n", "print(f\"Remaining rows: {len(df_clean):,}\")\n", "\n", "# Remove exact duplicates based on cleaned text\n", "print(\"\\n🔄 Removing duplicates...\")\n", "before_dedup = len(df_clean)\n", "df_clean = df_clean.drop_duplicates(subset=['Sentence_cleaned'], keep='first')\n", "duplicates_removed = before_dedup - len(df_clean)\n", "\n", "print(f\"Removed {duplicates_removed:,} duplicate entries\")\n", "print(f\"Final dataset size: {len(df_clean):,} rows\")\n", "\n", "# Calculate text statistics\n", "df_clean['text_length'] = df_clean['Sentence_cleaned'].str.len()\n", "df_clean['word_count'] = df_clean['Sentence_cleaned'].str.split().str.len()\n", "\n", "# Show cleaning examples\n", "print(f\"\\n✨ Text cleaning examples:\")\n", "for i in range(min(3, len(df_clean))):\n", "    idx = df_clean.index[i]\n", "    original = df.loc[idx, 'Sentence']\n", "    cleaned = df_clean.loc[idx, 'Sentence_cleaned']\n", "    \n", "    print(f\"\\nExample {i+1}:\")\n", "    print(f\"Original:  {original[:80]}...\")\n", "    print(f\"Cleaned:   {cleaned[:80]}...\")\n", "    print(f\"Sentiment: {df_clean.loc[idx, 'Sentiment']}\")\n", "\n", "# Show text statistics\n", "print(f\"\\n📊 Text Statistics After Cleaning:\")\n", "print(f\"Average text length: {df_clean['text_length'].mean():.1f} characters\")\n", "print(f\"Average word count: {df_clean['word_count'].mean():.1f} words\")\n", "print(f\"Text length range: {df_clean['text_length'].min()} - {df_clean['text_length'].max()} characters\")\n", "\n", "# Update sentiment distribution after cleaning\n", "print(f\"\\n📈 Sentiment distribution after cleaning:\")\n", "sentiment_after_cleaning = df_clean['Sentiment'].value_counts()\n", "print(sentiment_after_cleaning)\n", "\n", "# Reset index for clean dataset\n", "df_clean = df_clean.reset_index(drop=True)\n", "print(f\"\\n✅ Data cleaning completed successfully!\")\n", "print(f\"Ready for training: {len(df_clean):,} clean samples\")"]}, {"cell_type": "markdown", "id": "114aef44", "metadata": {}, "source": ["## **4. Label Encoding & Data Splitting**\n", "\n", "### **🏷️ Prepare Labels and Split Data for Training**"]}, {"cell_type": "code", "execution_count": null, "id": "09f61de3", "metadata": {}, "outputs": [], "source": ["# Label encoding for sentiment analysis\n", "print(\"🏷️ Encoding sentiment labels...\")\n", "\n", "# Check unique sentiment values\n", "unique_sentiments = df_clean['Sentiment'].unique()\n", "print(f\"Unique sentiment labels: {unique_sentiments}\")\n", "\n", "# Create label mapping (standard for financial sentiment analysis)\n", "label_mapping = {\n", "    'negative': 0,\n", "    'neutral': 1, \n", "    'positive': 2\n", "}\n", "\n", "def normalize_sentiment_label(sentiment):\n", "    \"\"\"Normalize sentiment labels to handle case variations\"\"\"\n", "    if pd.isna(sentiment):\n", "        return sentiment\n", "    \n", "    sentiment_str = str(sentiment).lower().strip()\n", "    \n", "    # Map variations to standard labels\n", "    if sentiment_str in ['negative', 'neg', 'bad', 'bearish', '-1']:\n", "        return 'negative'\n", "    elif sentiment_str in ['neutral', 'neu', 'none', 'neut', 'hold', '0']:\n", "        return 'neutral'\n", "    elif sentiment_str in ['positive', 'pos', 'good', 'bullish', 'buy', '1']:\n", "        return 'positive'\n", "    else:\n", "        return sentiment_str\n", "\n", "# Normalize sentiment labels\n", "df_clean['Sentiment_normalized'] = df_clean['Sentiment'].apply(normalize_sentiment_label)\n", "\n", "# Check for any unmapped labels\n", "unmapped_labels = df_clean[~df_clean['Sentiment_normalized'].isin(label_mapping.keys())]\n", "if len(unmapped_labels) > 0:\n", "    print(f\"⚠️ Found {len(unmapped_labels)} unmapped labels:\")\n", "    print(unmapped_labels['Sentiment_normalized'].value_counts())\n", "    \n", "    # Remove rows with unmapped labels\n", "    df_clean = df_clean[df_clean['Sentiment_normalized'].isin(label_mapping.keys())].copy()\n", "    print(f\"Removed unmapped labels. Remaining samples: {len(df_clean):,}\")\n", "\n", "# Apply label encoding\n", "df_clean['label'] = df_clean['Sentiment_normalized'].map(label_mapping)\n", "\n", "# Create reverse mapping for reference\n", "reverse_label_mapping = {v: k for k, v in label_mapping.items()}\n", "\n", "# Validate label encoding\n", "print(f\"\\n✅ Label encoding validation:\")\n", "print(f\"Label mapping: {label_mapping}\")\n", "print(f\"Reverse mapping: {reverse_label_mapping}\")\n", "\n", "# Check final label distribution\n", "label_distribution = df_clean['label'].value_counts().sort_index()\n", "print(f\"\\nFinal label distribution:\")\n", "for label, count in label_distribution.items():\n", "    sentiment_name = reverse_label_mapping[label]\n", "    percentage = (count / len(df_clean)) * 100\n", "    print(f\"  {label} ({sentiment_name}): {count:,} samples ({percentage:.2f}%)\")\n", "\n", "# Verify no missing labels\n", "missing_labels = df_clean['label'].isna().sum()\n", "if missing_labels > 0:\n", "    print(f\"⚠️ Found {missing_labels} missing labels - removing them\")\n", "    df_clean = df_clean.dropna(subset=['label']).copy()\n", "\n", "print(f\"\\n🎯 Label encoding completed!\")\n", "print(f\"Dataset ready for splitting: {len(df_clean):,} labeled samples\")"]}, {"cell_type": "code", "execution_count": null, "id": "22d11abb", "metadata": {}, "outputs": [], "source": ["# Stratified data splitting for balanced training\n", "print(\"🔄 Splitting data into train/validation/test sets...\")\n", "\n", "# Define split ratios\n", "train_ratio = 0.7    # 70% for training\n", "val_ratio = 0.15     # 15% for validation  \n", "test_ratio = 0.15    # 15% for testing\n", "\n", "print(f\"Split ratios: Train={train_ratio:.0%}, Validation={val_ratio:.0%}, Test={test_ratio:.0%}\")\n", "\n", "# Prepare data for splitting\n", "X = df_clean[['Sentence_cleaned']].copy()\n", "y = df_clean['label'].copy()\n", "\n", "# First split: separate test set (stratified)\n", "X_temp, X_test, y_temp, y_test = train_test_split(\n", "    X, y, \n", "    test_size=test_ratio, \n", "    stratify=y, \n", "    random_state=42\n", ")\n", "\n", "# Second split: separate train and validation (stratified)\n", "val_ratio_adjusted = val_ratio / (train_ratio + val_ratio)\n", "X_train, X_val, y_train, y_val = train_test_split(\n", "    X_temp, y_temp,\n", "    test_size=val_ratio_adjusted,\n", "    stratify=y_temp,\n", "    random_state=42\n", ")\n", "\n", "# Verify splits\n", "print(f\"\\n📊 Data split sizes:\")\n", "print(f\"Train: {len(X_train):,} samples ({len(X_train)/len(df_clean):.1%})\")\n", "print(f\"Validation: {len(X_val):,} samples ({len(X_val)/len(df_clean):.1%})\")\n", "print(f\"Test: {len(X_test):,} samples ({len(X_test)/len(df_clean):.1%})\")\n", "print(f\"Total: {len(X_train) + len(X_val) + len(X_test):,} samples\")\n", "\n", "# Check label distribution in each split\n", "def check_split_distribution(y_split, split_name):\n", "    \"\"\"Check and display label distribution for a split\"\"\"\n", "    distribution = y_split.value_counts().sort_index()\n", "    percentages = y_split.value_counts(normalize=True).sort_index() * 100\n", "    \n", "    print(f\"\\n{split_name} label distribution:\")\n", "    for label in sorted(distribution.index):\n", "        sentiment_name = reverse_label_mapping[label]\n", "        count = distribution[label]\n", "        percentage = percentages[label]\n", "        print(f\"  {label} ({sentiment_name}): {count:,} ({percentage:.1f}%)\")\n", "    \n", "    return distribution\n", "\n", "train_dist = check_split_distribution(y_train, \"Train\")\n", "val_dist = check_split_distribution(y_val, \"Validation\")\n", "test_dist = check_split_distribution(y_test, \"Test\")\n", "\n", "# Create final datasets\n", "train_data = pd.DataFrame({\n", "    'text': X_train['Sentence_cleaned'].values,\n", "    'label': y_train.values\n", "}).reset_index(drop=True)\n", "\n", "val_data = pd.DataFrame({\n", "    'text': X_val['Sentence_cleaned'].values,\n", "    'label': y_val.values  \n", "}).reset_index(drop=True)\n", "\n", "test_data = pd.DataFrame({\n", "    'text': X_test['Sentence_cleaned'].values,\n", "    'label': y_test.values\n", "}).reset_index(drop=True)\n", "\n", "# Check for data leakage\n", "print(f\"\\n🔍 Checking for data leakage...\")\n", "train_texts = set(train_data['text'])\n", "val_texts = set(val_data['text'])\n", "test_texts = set(test_data['text'])\n", "\n", "train_val_overlap = len(train_texts.intersection(val_texts))\n", "train_test_overlap = len(train_texts.intersection(test_texts))\n", "val_test_overlap = len(val_texts.intersection(test_texts))\n", "\n", "print(f\"Train-Validation overlap: {train_val_overlap} texts\")\n", "print(f\"Train-Test overlap: {train_test_overlap} texts\")\n", "print(f\"Validation-Test overlap: {val_test_overlap} texts\")\n", "\n", "if train_val_overlap == 0 and train_test_overlap == 0 and val_test_overlap == 0:\n", "    print(\"✅ No data leakage detected!\")\n", "else:\n", "    print(\"⚠️ Data leakage detected!\")\n", "\n", "print(f\"\\n✅ Data splitting completed successfully!\")\n", "print(f\"Ready for FinBERT tokenization\")"]}, {"cell_type": "markdown", "id": "c9ce09b4", "metadata": {}, "source": ["## **5. FinBERT Tokenization & Dataset Preparation**\n", "\n", "### **🤖 Load FinBERT Tokenizer and Prepare Datasets**"]}, {"cell_type": "code", "execution_count": null, "id": "4c73ad4c", "metadata": {}, "outputs": [], "source": ["# Load FinBERT tokenizer and analyze sequence lengths\n", "print(\"🤖 Loading FinBERT tokenizer...\")\n", "\n", "MODEL_NAME = \"ProsusAI/finbert\"\n", "MAX_LENGTH = 512  # Standard for FinBERT\n", "\n", "try:\n", "    tokenizer = AutoTokenizer.from_pretrained(MODEL_NAME)\n", "    print(f\"✅ FinBERT tokenizer loaded successfully!\")\n", "    print(f\"Model: {MODEL_NAME}\")\n", "    print(f\"Vocabulary size: {tokenizer.vocab_size:,}\")\n", "    print(f\"Max model length: {tokenizer.model_max_length}\")\n", "except Exception as e:\n", "    print(f\"❌ Error loading FinBERT tokenizer: {e}\")\n", "    print(\"🔄 Falling back to BERT tokenizer...\")\n", "    MODEL_NAME = \"bert-base-uncased\"\n", "    tokenizer = AutoTokenizer.from_pretrained(MODEL_NAME)\n", "\n", "# Analyze token lengths to determine optimal max_length\n", "print(f\"\\n📏 Analyzing token lengths for optimal sequence length...\")\n", "\n", "def analyze_token_lengths(texts, sample_size=1000):\n", "    \"\"\"Analyze token lengths in a sample of texts\"\"\"\n", "    \n", "    if len(texts) > sample_size:\n", "        sample_indices = np.random.choice(len(texts), sample_size, replace=False)\n", "        sample_texts = [texts.iloc[i] for i in sample_indices]\n", "    else:\n", "        sample_texts = texts.tolist()\n", "    \n", "    token_lengths = []\n", "    \n", "    print(f\"Analyzing {len(sample_texts)} samples...\")\n", "    for text in tqdm(sample_texts, desc=\"Tokenizing\"):\n", "        tokens = tokenizer(text, truncation=False, add_special_tokens=True)\n", "        token_lengths.append(len(tokens['input_ids']))\n", "    \n", "    return np.array(token_lengths)\n", "\n", "# Analyze token lengths across all data\n", "print(\"\\nAnalyzing token lengths across dataset...\")\n", "all_texts = pd.concat([train_data['text'], val_data['text'], test_data['text']])\n", "token_lengths = analyze_token_lengths(all_texts, sample_size=2000)\n", "\n", "# Calculate statistics\n", "stats = {\n", "    'mean': np.mean(token_lengths),\n", "    'median': np.median(token_lengths),\n", "    'std': np.std(token_lengths),\n", "    'min': np.min(token_lengths),\n", "    'max': np.max(token_lengths),\n", "    'percentile_95': np.percentile(token_lengths, 95),\n", "    'percentile_99': np.percentile(token_lengths, 99)\n", "}\n", "\n", "print(f\"\\n📊 Token Length Statistics:\")\n", "print(f\"  Mean: {stats['mean']:.1f} tokens\")\n", "print(f\"  Median: {stats['median']:.1f} tokens\")\n", "print(f\"  95th percentile: {stats['percentile_95']:.1f} tokens\")\n", "print(f\"  99th percentile: {stats['percentile_99']:.1f} tokens\")\n", "print(f\"  Max: {stats['max']} tokens\")\n", "\n", "# Determine optimal max_length\n", "recommended_max_length = int(np.percentile(token_lengths, 95))\n", "max_length_options = [128, 256, 512]\n", "optimal_max_length = min([x for x in max_length_options if x >= recommended_max_length], default=512)\n", "\n", "print(f\"\\nRecommended max_length: {recommended_max_length}\")\n", "print(f\"Chosen max_length: {optimal_max_length}\")\n", "\n", "# Calculate truncation impact\n", "truncated_samples = (token_lengths > optimal_max_length).sum()\n", "truncation_percentage = (truncated_samples / len(token_lengths)) * 100\n", "\n", "print(f\"Samples that will be truncated: {truncated_samples} ({truncation_percentage:.2f}%)\")\n", "\n", "# Visualize token length distribution\n", "plt.figure(figsize=(12, 6))\n", "\n", "plt.subplot(1, 2, 1)\n", "plt.hist(token_lengths, bins=50, alpha=0.7, edgecolor='black')\n", "plt.axvline(optimal_max_length, color='red', linestyle='--', \n", "            label=f'max_length={optimal_max_length}')\n", "plt.axvline(np.mean(token_lengths), color='green', linestyle='--', \n", "            label=f'Mean={np.mean(token_lengths):.1f}')\n", "plt.title('Token Length Distribution')\n", "plt.xlabel('Token Length')\n", "plt.ylabel('Frequency')\n", "plt.legend()\n", "\n", "plt.subplot(1, 2, 2)\n", "sorted_lengths = np.sort(token_lengths)\n", "cumulative_pct = np.arange(1, len(sorted_lengths) + 1) / len(sorted_lengths) * 100\n", "plt.plot(sorted_lengths, cumulative_pct)\n", "plt.axvline(optimal_max_length, color='red', linestyle='--', \n", "            label=f'max_length={optimal_max_length}')\n", "plt.title('Cumulative Token Length Distribution')\n", "plt.xlabel('Token Length')\n", "plt.ylabel('Cumulative Percentage')\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(f\"\\n✅ Token analysis completed!\")\n", "print(f\"Proceeding with max_length={optimal_max_length} for tokenization\")"]}, {"cell_type": "code", "execution_count": null, "id": "4889ad81", "metadata": {}, "outputs": [], "source": ["# Create HuggingFace datasets with tokenization\n", "print(\"🔄 Creating tokenized HuggingFace datasets...\")\n", "\n", "def tokenize_function(examples):\n", "    \"\"\"Tokenize text examples for FinBERT training\"\"\"\n", "    return tokenizer(\n", "        examples['text'],\n", "        truncation=True,\n", "        padding=False,  # <PERSON> pad dynamically during training\n", "        max_length=optimal_max_length,\n", "        return_tensors=None\n", "    )\n", "\n", "# Create HuggingFace datasets\n", "print(\"Creating datasets...\")\n", "train_dataset = Dataset.from_pandas(train_data)\n", "val_dataset = Dataset.from_pandas(val_data)\n", "test_dataset = Dataset.from_pandas(test_data)\n", "\n", "# Apply tokenization\n", "print(\"Tokenizing training set...\")\n", "train_dataset = train_dataset.map(\n", "    tokenize_function, \n", "    batched=True, \n", "    desc=\"Tokenizing train\"\n", ")\n", "\n", "print(\"Tokenizing validation set...\")\n", "val_dataset = val_dataset.map(\n", "    tokenize_function, \n", "    batched=True, \n", "    desc=\"Tokenizing validation\"\n", ")\n", "\n", "print(\"Tokenizing test set...\")\n", "test_dataset = test_dataset.map(\n", "    tokenize_function, \n", "    batched=True, \n", "    desc=\"Tokenizing test\"\n", ")\n", "\n", "# Create DatasetDict\n", "tokenized_datasets = DatasetDict({\n", "    'train': train_dataset,\n", "    'validation': val_dataset,\n", "    'test': test_dataset\n", "})\n", "\n", "# Remove text column as it's no longer needed\n", "tokenized_datasets = tokenized_datasets.remove_columns(['text'])\n", "\n", "print(f\"\\n✅ Tokenized datasets created:\")\n", "print(f\"Dataset structure: {tokenized_datasets}\")\n", "\n", "# Verify tokenization\n", "print(f\"\\n🔍 Dataset verification:\")\n", "for split_name, dataset in tokenized_datasets.items():\n", "    print(f\"\\n{split_name.capitalize()} dataset:\")\n", "    print(f\"  Size: {len(dataset):,}\")\n", "    print(f\"  Features: {dataset.features}\")\n", "    \n", "    # Check a sample\n", "    sample = dataset[0]\n", "    print(f\"  Sample input_ids length: {len(sample['input_ids'])}\")\n", "    print(f\"  Sample attention_mask length: {len(sample['attention_mask'])}\")\n", "    print(f\"  Sample label: {sample['label']} ({reverse_label_mapping[sample['label']]})\")\n", "\n", "# Show example of tokenized data\n", "print(f\"\\n📝 Tokenization example:\")\n", "sample = tokenized_datasets['train'][0]\n", "input_ids = sample['input_ids']\n", "attention_mask = sample['attention_mask']\n", "label = sample['label']\n", "\n", "print(f\"Label: {label} ({reverse_label_mapping[label]})\")\n", "print(f\"Input IDs (first 20): {input_ids[:20]}\")\n", "print(f\"Attention mask (first 20): {attention_mask[:20]}\")\n", "\n", "# Decode to verify\n", "decoded_text = tokenizer.decode(input_ids, skip_special_tokens=True)\n", "print(f\"Decoded text: {decoded_text[:100]}...\")\n", "\n", "# Actual vs padding tokens\n", "actual_tokens = sum(attention_mask)\n", "padding_tokens = len(attention_mask) - actual_tokens\n", "print(f\"Actual tokens: {actual_tokens}, Padding tokens: {padding_tokens}\")\n", "\n", "print(f\"\\n✅ Tokenization completed successfully!\")\n", "print(f\"Datasets ready for FinBERT training\")"]}, {"cell_type": "markdown", "id": "55e471f1", "metadata": {}, "source": ["## **6. FinBERT Model Configuration & Training Setup**\n", "\n", "### **🏗️ Configure FinBERT Model and Training Parameters**"]}, {"cell_type": "code", "execution_count": null, "id": "a14f6183", "metadata": {}, "outputs": [], "source": ["# Load and configure FinBERT model for sequence classification\n", "print(\"🏗️ Loading FinBERT model for sentiment classification...\")\n", "\n", "# Model configuration\n", "num_labels = 3  # negative, neutral, positive\n", "id2label = reverse_label_mapping\n", "label2id = label_mapping\n", "\n", "try:\n", "    # Load model configuration\n", "    config = AutoConfig.from_pretrained(\n", "        MODEL_NAME,\n", "        num_labels=num_labels,\n", "        id2label=id2label,\n", "        label2id=label2id,\n", "        finetuning_task=\"sentiment-analysis\"\n", "    )\n", "    \n", "    # Load pre-trained model\n", "    model = AutoModelForSequenceClassification.from_pretrained(\n", "        MODEL_NAME,\n", "        config=config,\n", "        ignore_mismatched_sizes=True  # Handle size mismatches for classification head\n", "    )\n", "    \n", "    print(f\"✅ FinBERT model loaded successfully!\")\n", "    print(f\"Model: {MODEL_NAME}\")\n", "    print(f\"Number of labels: {num_labels}\")\n", "    print(f\"Label mapping: {label2id}\")\n", "    \n", "except Exception as e:\n", "    print(f\"❌ Error loading FinBERT model: {e}\")\n", "    print(\"🔄 This might happen if the model doesn't exist - check MODEL_NAME\")\n", "    raise\n", "\n", "# Move model to GPU if available\n", "model = model.to(device)\n", "print(f\"📱 Model moved to: {device}\")\n", "\n", "# Display model information\n", "print(f\"\\n📊 Model Information:\")\n", "total_params = sum(p.numel() for p in model.parameters())\n", "trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)\n", "\n", "print(f\"  Total parameters: {total_params:,}\")\n", "print(f\"  Trainable parameters: {trainable_params:,}\")\n", "print(f\"  Model size: ~{total_params * 4 / 1024**2:.1f} MB\")\n", "\n", "# Check model architecture\n", "print(f\"\\n🏗️ Model Architecture:\")\n", "print(f\"  Base model: {model.bert.__class__.__name__}\")\n", "print(f\"  Classification head: {model.classifier}\")\n", "print(f\"  Hidden size: {model.config.hidden_size}\")\n", "print(f\"  Number of attention heads: {model.config.num_attention_heads}\")\n", "print(f\"  Number of hidden layers: {model.config.num_hidden_layers}\")\n", "\n", "print(f\"\\n✅ Model configuration completed!\")"]}, {"cell_type": "code", "execution_count": null, "id": "97e14ed2", "metadata": {}, "outputs": [], "source": ["# Configure training arguments and data collator\n", "print(\"⚙️ Setting up training configuration...\")\n", "\n", "# Training hyperparameters (optimized for FinBERT sentiment analysis)\n", "BATCH_SIZE = 16 if torch.cuda.is_available() else 8\n", "LEARNING_RATE = 2e-5  # Standard learning rate for FinBERT fine-tuning\n", "NUM_EPOCHS = 3  # Typically 2-4 epochs for fine-tuning\n", "WARMUP_STEPS = 500\n", "WEIGHT_DECAY = 0.01\n", "SAVE_STRATEGY = \"epoch\"\n", "EVALUATION_STRATEGY = \"epoch\"\n", "LOGGING_STEPS = 100\n", "\n", "# Output directory for model checkpoints\n", "OUTPUT_DIR = \"./finbert-sentiment-finetuned\"\n", "\n", "# Training arguments\n", "training_args = TrainingArguments(\n", "    output_dir=OUTPUT_DIR,\n", "    num_train_epochs=NUM_EPOCHS,\n", "    per_device_train_batch_size=BATCH_SIZE,\n", "    per_device_eval_batch_size=BATCH_SIZE,\n", "    warmup_steps=WARMUP_STEPS,\n", "    weight_decay=WEIGHT_DECAY,\n", "    learning_rate=LEARNING_RATE,\n", "    logging_dir='./logs',\n", "    logging_steps=LOGGING_STEPS,\n", "    evaluation_strategy=EVALUATION_STRATEGY,\n", "    save_strategy=SAVE_STRATEGY,\n", "    save_total_limit=2,  # Keep only best 2 checkpoints\n", "    load_best_model_at_end=True,\n", "    metric_for_best_model=\"eval_accuracy\",\n", "    greater_is_better=True,\n", "    push_to_hub=False,  # Set to True if you want to push to HuggingFace Hub\n", "    report_to=\"none\",  # Disable wandb logging for simplicity\n", "    dataloader_pin_memory=False,\n", "    fp16=torch.cuda.is_available(),  # Enable mixed precision if GPU available\n", "    gradient_checkpointing=True,  # Save memory\n", "    dataloader_num_workers=0,  # Set to 0 for Colab compatibility\n", "    remove_unused_columns=False,\n", ")\n", "\n", "print(f\"✅ Training arguments configured:\")\n", "print(f\"  Batch size: {BATCH_SIZE}\")\n", "print(f\"  Learning rate: {LEARNING_RATE}\")\n", "print(f\"  Number of epochs: {NUM_EPOCHS}\")\n", "print(f\"  Warmup steps: {WARMUP_STEPS}\")\n", "print(f\"  Output directory: {OUTPUT_DIR}\")\n", "print(f\"  Mixed precision (FP16): {training_args.fp16}\")\n", "\n", "# Data collator for dynamic padding\n", "data_collator = DataCollatorWithPadding(\n", "    tokenizer=tokenizer,\n", "    padding=True,\n", "    max_length=optimal_max_length,\n", "    pad_to_multiple_of=8 if training_args.fp16 else None,\n", ")\n", "\n", "print(f\"✅ Data collator configured for dynamic padding\")\n", "\n", "# Define evaluation metrics\n", "def compute_metrics(eval_pred):\n", "    \"\"\"Compute accuracy and F1 scores for evaluation\"\"\"\n", "    predictions, labels = eval_pred\n", "    predictions = np.argmax(predictions, axis=1)\n", "    \n", "    # Calculate metrics\n", "    accuracy = accuracy_score(labels, predictions)\n", "    \n", "    # Classification report\n", "    report = classification_report(\n", "        labels, \n", "        predictions, \n", "        target_names=[reverse_label_mapping[i] for i in sorted(reverse_label_mapping.keys())],\n", "        output_dict=True,\n", "        zero_division=0\n", "    )\n", "    \n", "    return {\n", "        \"accuracy\": accuracy,\n", "        \"f1_macro\": report[\"macro avg\"][\"f1-score\"],\n", "        \"f1_weighted\": report[\"weighted avg\"][\"f1-score\"],\n", "        \"precision_macro\": report[\"macro avg\"][\"precision\"],\n", "        \"recall_macro\": report[\"macro avg\"][\"recall\"]\n", "    }\n", "\n", "print(f\"✅ Evaluation metrics function defined\")\n", "print(f\"Metrics: accuracy, f1_macro, f1_weighted, precision_macro, recall_macro\")\n", "\n", "print(f\"\\n🎯 Training setup completed!\")\n", "print(f\"Ready to initialize <PERSON><PERSON> and begin fine-tuning\")"]}, {"cell_type": "markdown", "id": "aa4d68f9", "metadata": {}, "source": ["## **7. Model Training & Fine-tuning**\n", "\n", "### **🏋️ Fine-tune FinBERT on Financial Sentiment Data**"]}, {"cell_type": "code", "execution_count": null, "id": "6f08ecb3", "metadata": {}, "outputs": [], "source": ["# Initialize Trainer and start fine-tuning\n", "print(\"🏋️ Initializing FinBERT Trainer...\")\n", "\n", "# Initialize the Trainer\n", "trainer = Trainer(\n", "    model=model,\n", "    args=training_args,\n", "    train_dataset=tokenized_datasets['train'],\n", "    eval_dataset=tokenized_datasets['validation'],\n", "    tokenizer=tokenizer,\n", "    data_collator=data_collator,\n", "    compute_metrics=compute_metrics,\n", "    callbacks=[EarlyStoppingCallback(early_stopping_patience=2)]\n", ")\n", "\n", "print(f\"✅ Trainer initialized successfully!\")\n", "print(f\"📊 Training dataset size: {len(tokenized_datasets['train']):,}\")\n", "print(f\"📊 Validation dataset size: {len(tokenized_datasets['validation']):,}\")\n", "\n", "# Calculate estimated training time\n", "steps_per_epoch = len(tokenized_datasets['train']) // BATCH_SIZE\n", "total_steps = steps_per_epoch * NUM_EPOCHS\n", "estimated_time_hours = total_steps * 2 / 3600  # Rough estimate: 2 seconds per step\n", "\n", "print(f\"\\n⏱️ Training Estimates:\")\n", "print(f\"  Steps per epoch: {steps_per_epoch}\")\n", "print(f\"  Total training steps: {total_steps}\")\n", "print(f\"  Estimated training time: {estimated_time_hours:.1f} hours\")\n", "\n", "print(f\"\\n🚀 Starting FinBERT fine-tuning...\")\n", "print(f\"📈 Training progress will be displayed below:\")\n", "\n", "# Record start time\n", "start_time = time.time()\n", "\n", "# Start training\n", "try:\n", "    trainer.train()\n", "    \n", "    # Calculate actual training time\n", "    end_time = time.time()\n", "    training_time = end_time - start_time\n", "    \n", "    print(f\"\\n🎉 Training completed successfully!\")\n", "    print(f\"⏱️ Total training time: {training_time/3600:.2f} hours ({training_time/60:.1f} minutes)\")\n", "    \n", "except Exception as e:\n", "    print(f\"❌ Training failed with error: {e}\")\n", "    raise\n", "\n", "# Get training history\n", "train_history = trainer.state.log_history\n", "\n", "print(f\"\\n📈 Training Summary:\")\n", "print(f\"  Total epochs completed: {trainer.state.epoch}\")\n", "print(f\"  Total steps: {trainer.state.global_step}\")\n", "print(f\"  Best model metric: {trainer.state.best_metric:.4f}\")\n", "\n", "# Display final metrics\n", "if train_history:\n", "    final_train_loss = None\n", "    final_eval_metrics = None\n", "    \n", "    for log in reversed(train_history):\n", "        if 'train_loss' in log and final_train_loss is None:\n", "            final_train_loss = log['train_loss']\n", "        if 'eval_accuracy' in log and final_eval_metrics is None:\n", "            final_eval_metrics = {k: v for k, v in log.items() if k.startswith('eval_')}\n", "        \n", "        if final_train_loss is not None and final_eval_metrics is not None:\n", "            break\n", "    \n", "    if final_train_loss:\n", "        print(f\"  Final training loss: {final_train_loss:.4f}\")\n", "    \n", "    if final_eval_metrics:\n", "        print(f\"  Final validation metrics:\")\n", "        for metric, value in final_eval_metrics.items():\n", "            print(f\"    {metric}: {value:.4f}\")\n", "\n", "print(f\"\\n✅ FinBERT fine-tuning completed!\")"]}, {"cell_type": "code", "execution_count": null, "id": "54393fce", "metadata": {}, "outputs": [], "source": ["# Visualize training progress and metrics\n", "print(\"📊 Analyzing training progress...\")\n", "\n", "# Extract training metrics from log history\n", "train_losses = []\n", "eval_losses = []\n", "eval_accuracies = []\n", "eval_f1_scores = []\n", "steps = []\n", "epochs = []\n", "\n", "for log in train_history:\n", "    if 'train_loss' in log:\n", "        train_losses.append(log['train_loss'])\n", "        steps.append(log['step'])\n", "    \n", "    if 'eval_loss' in log:\n", "        eval_losses.append(log['eval_loss'])\n", "        eval_accuracies.append(log['eval_accuracy'])\n", "        eval_f1_scores.append(log['eval_f1_macro'])\n", "        epochs.append(log['epoch'])\n", "\n", "# Create comprehensive training visualization\n", "fig, axes = plt.subplots(2, 2, figsize=(16, 12))\n", "\n", "# Training Loss\n", "axes[0,0].plot(steps, train_losses, 'b-', label='Training Loss', linewidth=2)\n", "axes[0,0].set_title('Training Loss Over Steps', fontsize=14, fontweight='bold')\n", "axes[0,0].set_xlabel('Steps')\n", "axes[0,0].set_ylabel('Loss')\n", "axes[0,0].legend()\n", "axes[0,0].grid(True, alpha=0.3)\n", "\n", "# Validation Loss\n", "if eval_losses:\n", "    axes[0,1].plot(epochs, eval_losses, 'r-', label='Validation Loss', linewidth=2, marker='o')\n", "    axes[0,1].set_title('Validation Loss Over Epochs', fontsize=14, fontweight='bold')\n", "    axes[0,1].set_xlabel('Epoch')\n", "    axes[0,1].set_ylabel('Loss')\n", "    axes[0,1].legend()\n", "    axes[0,1].grid(True, alpha=0.3)\n", "\n", "# Validation Accuracy\n", "if eval_accuracies:\n", "    axes[1,0].plot(epochs, eval_accuracies, 'g-', label='Validation Accuracy', linewidth=2, marker='o')\n", "    axes[1,0].set_title('Validation Accuracy Over Epochs', fontsize=14, fontweight='bold')\n", "    axes[1,0].set_xlabel('Epoch')\n", "    axes[1,0].set_ylabel('Accuracy')\n", "    axes[1,0].set_ylim(0, 1)\n", "    axes[1,0].legend()\n", "    axes[1,0].grid(True, alpha=0.3)\n", "\n", "# Validation F1 Score\n", "if eval_f1_scores:\n", "    axes[1,1].plot(epochs, eval_f1_scores, 'purple', label='Validation F1 (Macro)', linewidth=2, marker='o')\n", "    axes[1,1].set_title('Validation F1 Score Over Epochs', fontsize=14, fontweight='bold')\n", "    axes[1,1].set_xlabel('Epoch')\n", "    axes[1,1].set_ylabel('F1 Score')\n", "    axes[1,1].set_ylim(0, 1)\n", "    axes[1,1].legend()\n", "    axes[1,1].grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Training summary statistics\n", "if train_losses:\n", "    print(f\"\\n📈 Training Progress Summary:\")\n", "    print(f\"  Initial training loss: {train_losses[0]:.4f}\")\n", "    print(f\"  Final training loss: {train_losses[-1]:.4f}\")\n", "    print(f\"  Loss reduction: {((train_losses[0] - train_losses[-1]) / train_losses[0] * 100):.1f}%\")\n", "\n", "if eval_accuracies:\n", "    print(f\"\\n🎯 Validation Performance:\")\n", "    print(f\"  Best accuracy: {max(eval_accuracies):.4f}\")\n", "    print(f\"  Final accuracy: {eval_accuracies[-1]:.4f}\")\n", "    print(f\"  Best F1 score: {max(eval_f1_scores):.4f}\")\n", "    print(f\"  Final F1 score: {eval_f1_scores[-1]:.4f}\")\n", "\n", "# Check for overfitting signs\n", "if len(eval_losses) >= 2:\n", "    if eval_losses[-1] > eval_losses[-2]:\n", "        print(f\"\\n⚠️ Warning: Validation loss increased in final epoch - possible overfitting\")\n", "    else:\n", "        print(f\"\\n✅ Validation loss decreased or stabilized - good generalization\")\n", "\n", "print(f\"\\n🎉 Training analysis completed!\")"]}, {"cell_type": "markdown", "id": "eba6c07c", "metadata": {}, "source": ["## **8. Model Evaluation & Testing**\n", "\n", "### **🎯 <PERSON><PERSON>ate Fine-tuned FinBERT on Test Set**"]}, {"cell_type": "code", "execution_count": null, "id": "86bdbae2", "metadata": {}, "outputs": [], "source": ["# Evaluate the fine-tuned model on test set\n", "print(\"🎯 Evaluating fine-tuned FinBERT on test set...\")\n", "\n", "# Evaluate on test set\n", "test_results = trainer.evaluate(eval_dataset=tokenized_datasets['test'])\n", "\n", "print(f\"\\n📊 Test Set Results:\")\n", "for metric, value in test_results.items():\n", "    if metric.startswith('eval_'):\n", "        metric_name = metric.replace('eval_', '').replace('_', ' ').title()\n", "        print(f\"  {metric_name}: {value:.4f}\")\n", "\n", "# Get predictions for detailed analysis\n", "print(f\"\\n🔍 Generating detailed predictions...\")\n", "\n", "# Make predictions\n", "predictions = trainer.predict(tokenized_datasets['test'])\n", "y_pred = np.argmax(predictions.predictions, axis=1)\n", "y_true = predictions.label_ids\n", "\n", "# Calculate comprehensive metrics\n", "test_accuracy = accuracy_score(y_true, y_pred)\n", "test_report = classification_report(\n", "    y_true, \n", "    y_pred, \n", "    target_names=[reverse_label_mapping[i] for i in sorted(reverse_label_mapping.keys())],\n", "    output_dict=True,\n", "    zero_division=0\n", ")\n", "\n", "print(f\"\\n📈 Detailed Test Set Performance:\")\n", "print(f\"  Overall Accuracy: {test_accuracy:.4f}\")\n", "print(f\"  Macro F1 Score: {test_report['macro avg']['f1-score']:.4f}\")\n", "print(f\"  Weighted F1 Score: {test_report['weighted avg']['f1-score']:.4f}\")\n", "\n", "print(f\"\\n📋 Per-Class Performance:\")\n", "for label_num in sorted(reverse_label_mapping.keys()):\n", "    label_name = reverse_label_mapping[label_num]\n", "    metrics = test_report[label_name]\n", "    print(f\"  {label_name.capitalize()}:\")\n", "    print(f\"    Precision: {metrics['precision']:.4f}\")\n", "    print(f\"    Recall: {metrics['recall']:.4f}\")\n", "    print(f\"    F1-Score: {metrics['f1-score']:.4f}\")\n", "    print(f\"    Support: {int(metrics['support'])}\")\n", "\n", "# Confusion Matrix\n", "cm = confusion_matrix(y_true, y_pred)\n", "print(f\"\\n🔢 Confusion Matrix:\")\n", "print(f\"    Predicted:\")\n", "print(f\"         Neg  Neu  Pos\")\n", "for i, label in enumerate(['Neg', 'Neu', 'Pos']):\n", "    print(f\"True {label}: {cm[i]}\")\n", "\n", "# Visualize confusion matrix\n", "plt.figure(figsize=(10, 8))\n", "sns.heatmap(\n", "    cm, \n", "    annot=True, \n", "    fmt='d', \n", "    cmap='Blues',\n", "    xticklabels=[reverse_label_mapping[i] for i in sorted(reverse_label_mapping.keys())],\n", "    yticklabels=[reverse_label_mapping[i] for i in sorted(reverse_label_mapping.keys())]\n", ")\n", "plt.title('Confusion Matrix - FinBERT Sentiment Classification', fontsize=16, fontweight='bold')\n", "plt.xlabel('Predicted Sentiment', fontsize=12)\n", "plt.ylabel('True Sentiment', fontsize=12)\n", "plt.show()\n", "\n", "# Calculate and show prediction confidence scores\n", "prediction_probs = torch.nn.functional.softmax(torch.tensor(predictions.predictions), dim=1)\n", "confidence_scores = torch.max(prediction_probs, dim=1)[0]\n", "\n", "print(f\"\\n🎯 Prediction Confidence Analysis:\")\n", "print(f\"  Average confidence: {confidence_scores.mean():.4f}\")\n", "print(f\"  Median confidence: {confidence_scores.median():.4f}\")\n", "print(f\"  Min confidence: {confidence_scores.min():.4f}\")\n", "print(f\"  Max confidence: {confidence_scores.max():.4f}\")\n", "\n", "# Show some example predictions\n", "print(f\"\\n📝 Sample Predictions:\")\n", "test_texts = test_data['text'].tolist()\n", "sample_indices = np.random.choice(len(y_pred), 5, replace=False)\n", "\n", "for i, idx in enumerate(sample_indices):\n", "    text = test_texts[idx]\n", "    true_label = reverse_label_mapping[y_true[idx]]\n", "    pred_label = reverse_label_mapping[y_pred[idx]]\n", "    confidence = confidence_scores[idx].item()\n", "    \n", "    print(f\"\\nExample {i+1}:\")\n", "    print(f\"  Text: {text[:100]}...\")\n", "    print(f\"  True sentiment: {true_label}\")\n", "    print(f\"  Predicted sentiment: {pred_label}\")\n", "    print(f\"  Confidence: {confidence:.4f}\")\n", "    print(f\"  Correct: {'✅' if true_label == pred_label else '❌'}\")\n", "\n", "print(f\"\\n✅ Model evaluation completed!\")"]}, {"cell_type": "markdown", "id": "8fef1993", "metadata": {}, "source": ["## **9. Model Saving & Final Summary**\n", "\n", "### **💾 Save Fine-tuned Model and Create Summary**"]}, {"cell_type": "code", "execution_count": null, "id": "f643304e", "metadata": {}, "outputs": [], "source": ["# Save the fine-tuned model and tokenizer\n", "print(\"💾 Saving fine-tuned FinBERT model...\")\n", "\n", "# Save the best model\n", "final_model_dir = \"./finbert-sentiment-final\"\n", "trainer.save_model(final_model_dir)\n", "tokenizer.save_pretrained(final_model_dir)\n", "\n", "print(f\"✅ Model saved to: {final_model_dir}\")\n", "\n", "# Create model card and configuration\n", "model_info = {\n", "    \"model_name\": \"FinBERT Fine-tuned for Financial Sentiment Analysis\",\n", "    \"base_model\": MODEL_NAME,\n", "    \"task\": \"Financial Sentiment Classification\",\n", "    \"dataset_size\": len(df_clean),\n", "    \"train_size\": len(tokenized_datasets['train']),\n", "    \"val_size\": len(tokenized_datasets['validation']),\n", "    \"test_size\": len(tokenized_datasets['test']),\n", "    \"num_labels\": num_labels,\n", "    \"label_mapping\": label_mapping,\n", "    \"max_length\": optimal_max_length,\n", "    \"training_time_hours\": training_time/3600,\n", "    \"test_accuracy\": test_accuracy,\n", "    \"test_f1_macro\": test_report['macro avg']['f1-score'],\n", "    \"test_f1_weighted\": test_report['weighted avg']['f1-score'],\n", "    \"training_params\": {\n", "        \"learning_rate\": LEARNING_RATE,\n", "        \"batch_size\": BATCH_SIZE,\n", "        \"num_epochs\": NUM_EPOCHS,\n", "        \"warmup_steps\": WARMUP_STEPS,\n", "        \"weight_decay\": WEIGHT_DECAY\n", "    },\n", "    \"timestamp\": datetime.now().isoformat()\n", "}\n", "\n", "# Save model information\n", "with open(f\"{final_model_dir}/model_info.json\", 'w') as f:\n", "    json.dump(model_info, f, indent=2)\n", "\n", "print(f\"✅ Model information saved\")\n", "\n", "# Download model files for local use\n", "print(f\"\\n📁 Model files ready for download:\")\n", "print(f\"  Location: {final_model_dir}/\")\n", "print(f\"  Files: config.json, pytorch_model.bin, tokenizer files, model_info.json\")\n", "\n", "# Create README for the model\n", "readme_content = f\"\"\"# FinBERT Financial Sentiment Analysis Model\n", "\n", "## Model Description\n", "This is a fine-tuned version of FinBERT (`{MODEL_NAME}`) for financial sentiment analysis.\n", "\n", "## Model Performance\n", "- **Test Accuracy**: {test_accuracy:.4f}\n", "- **Test F1 (Macro)**: {test_report['macro avg']['f1-score']:.4f}\n", "- **Test F1 (Weighted)**: {test_report['weighted avg']['f1-score']:.4f}\n", "\n", "## Dataset\n", "- **Total Samples**: {len(df_clean):,}\n", "- **Training Samples**: {len(tokenized_datasets['train']):,}\n", "- **Validation Samples**: {len(tokenized_datasets['validation']):,}\n", "- **Test Samples**: {len(tokenized_datasets['test']):,}\n", "\n", "## Label Mapping\n", "- 0: negative\n", "- 1: neutral\n", "- 2: positive\n", "\n", "## Usage\n", "\n", "```python\n", "from transformers import AutoTokenizer, AutoModelForSequenceClassification\n", "import torch\n", "\n", "# Load model and tokenizer\n", "tokenizer = AutoTokenizer.from_pretrained(\"./finbert-sentiment-final\")\n", "model = AutoModelForSequenceClassification.from_pretrained(\"./finbert-sentiment-final\")\n", "\n", "# Example prediction\n", "text = \"The company reported strong quarterly earnings.\"\n", "inputs = tokenizer(text, return_tensors=\"pt\", truncation=True, padding=True, max_length={optimal_max_length})\n", "\n", "with torch.no_grad():\n", "    outputs = model(**inputs)\n", "    predictions = torch.nn.functional.softmax(outputs.logits, dim=-1)\n", "    predicted_class = torch.argmax(predictions, dim=-1).item()\n", "\n", "# Map prediction to sentiment\n", "sentiment_map = {{0: \"negative\", 1: \"neutral\", 2: \"positive\"}}\n", "predicted_sentiment = sentiment_map[predicted_class]\n", "confidence = predictions[0][predicted_class].item()\n", "\n", "print(f\"Sentiment: {{predicted_sentiment}} (confidence: {{confidence:.4f}})\")\n", "```\n", "\n", "## Training Details\n", "- **Base Model**: {MODEL_NAME}\n", "- **Training Time**: {training_time/3600:.2f} hours\n", "- **Learning Rate**: {LEARNING_RATE}\n", "- **<PERSON><PERSON> Size**: {BATCH_SIZE}\n", "- **Epochs**: {NUM_EPOCHS}\n", "\n", "## Fine-tuning Date\n", "{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n", "\"\"\"\n", "\n", "with open(f\"{final_model_dir}/README.md\", 'w') as f:\n", "    f.write(readme_content)\n", "\n", "print(f\"✅ README.md created\")\n", "\n", "# Final summary\n", "print(f\"\\n{'='*60}\")\n", "print(f\"🎉 FINBERT TRAINING COMPLETED SUCCESSFULLY!\")\n", "print(f\"{'='*60}\")\n", "\n", "print(f\"\\n📊 Final Results Summary:\")\n", "print(f\"  Dataset processed: {len(df_clean):,} samples\")\n", "print(f\"  Training time: {training_time/3600:.2f} hours\")\n", "print(f\"  Test accuracy: {test_accuracy:.4f}\")\n", "print(f\"  Test F1 (macro): {test_report['macro avg']['f1-score']:.4f}\")\n", "print(f\"  Model saved to: {final_model_dir}\")\n", "\n", "print(f\"\\n🎯 Model Performance by Class:\")\n", "for label_num in sorted(reverse_label_mapping.keys()):\n", "    label_name = reverse_label_mapping[label_num]\n", "    f1_score = test_report[label_name]['f1-score']\n", "    print(f\"  {label_name.capitalize()}: F1 = {f1_score:.4f}\")\n", "\n", "print(f\"\\n💡 Next Steps:\")\n", "print(f\"  1. Download model files from {final_model_dir}/\")\n", "print(f\"  2. Use the model for financial sentiment prediction\")\n", "print(f\"  3. Consider further fine-tuning with domain-specific data\")\n", "print(f\"  4. Deploy the model for production use\")\n", "\n", "print(f\"\\n🚀 Your FinBERT model is ready for financial sentiment analysis!\")\n", "\n", "# Show quick test example\n", "print(f\"\\n🧪 Quick Test Example:\")\n", "test_text = \"The stock price increased significantly after the earnings announcement.\"\n", "inputs = tokenizer(test_text, return_tensors=\"pt\", truncation=True, padding=True, max_length=optimal_max_length)\n", "\n", "with torch.no_grad():\n", "    outputs = model(**inputs)\n", "    predictions = torch.nn.functional.softmax(outputs.logits, dim=-1)\n", "    predicted_class = torch.argmax(predictions, dim=-1).item()\n", "    confidence = predictions[0][predicted_class].item()\n", "\n", "predicted_sentiment = reverse_label_mapping[predicted_class]\n", "print(f\"  Text: {test_text}\")\n", "print(f\"  Predicted sentiment: {predicted_sentiment}\")\n", "print(f\"  Confidence: {confidence:.4f}\")\n", "\n", "print(f\"\\n✨ Training pipeline completed! ✨\")"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}
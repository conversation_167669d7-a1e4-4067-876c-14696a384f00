# Check GPU availability and setup
import torch
import os

print("🔍 Environment Check:")
print(f"PyTorch version: {torch.__version__}")
print(f"CUDA available: {torch.cuda.is_available()}")

if torch.cuda.is_available():
    print(f"CUDA version: {torch.version.cuda}")
    print(f"GPU device: {torch.cuda.get_device_name(0)}")
    print(f"GPU memory: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
    device = torch.device("cuda")
    print("✅ GPU acceleration enabled!")
else:
    device = torch.device("cpu")
    print("⚠️ GPU not available, using CPU (training will be slower)")

# Set memory optimization for large models
if torch.cuda.is_available():
    torch.cuda.empty_cache()
    print("🧹 GPU cache cleared")

print(f"\n🎯 Using device: {device}")

# Install required packages
!pip install transformers datasets accelerate evaluate scikit-learn
!pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
!pip install matplotlib seaborn plotly wordcloud

print("✅ All packages installed successfully!")

# Import all required libraries
import pandas as pd
import numpy as np
import re
import os
import json
import pickle
from collections import Counter
from typing import Dict, List, Tuple
import warnings
warnings.filterwarnings('ignore')

# Machine Learning and NLP libraries
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score
from sklearn.utils import shuffle

# HuggingFace Transformers and Datasets
from transformers import (
    AutoTokenizer, 
    AutoConfig, 
    AutoModelForSequenceClassification,
    TrainingArguments,
    Trainer,
    EarlyStoppingCallback,
    DataCollatorWithPadding
)
from datasets import Dataset, DatasetDict, load_metric
import torch
from torch.utils.data import DataLoader
import torch.nn.functional as F

# Visualization libraries
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go

# Text preprocessing
from html import unescape
from unicodedata import normalize

# Training utilities
from datetime import datetime
import time
from tqdm.auto import tqdm

# Set style for better visualizations
plt.style.use('default')
sns.set_palette("husl")

print("✅ All libraries imported successfully!")
print(f"🔥 Ready for FinBERT training on {device}!")

# Upload data file in Google Colab
from google.colab import files
import io

print("📂 Please upload your data.csv file:")
print("Expected format: CSV with 'Sentence' and 'Sentiment' columns")

# Upload file
uploaded = files.upload()

# Get the uploaded file
filename = list(uploaded.keys())[0]
print(f"\n✅ File uploaded: {filename}")

# Load the CSV data
try:
    df = pd.read_csv(io.BytesIO(uploaded[filename]), encoding='utf-8')
    print(f"✅ Data loaded successfully!")
except UnicodeDecodeError:
    print("⚠️ UTF-8 encoding failed, trying with latin-1...")
    df = pd.read_csv(io.BytesIO(uploaded[filename]), encoding='latin-1')
    print(f"✅ Data loaded with latin-1 encoding!")

# Basic dataset information
print(f"\n📈 Dataset Overview:")
print(f"Shape: {df.shape}")
print(f"Columns: {list(df.columns)}")
print(f"Memory usage: {df.memory_usage(deep=True).sum() / 1024**2:.2f} MB")

# Display first few rows
print(f"\n📝 First 5 rows:")
display(df.head())

print(f"\n📊 Data Types:")
print(df.dtypes)

# Data quality analysis
print("🔍 Data Quality Analysis:")

# Check for missing values
print(f"\n❓ Missing values:")
missing_data = df.isnull().sum()
for col, missing_count in missing_data.items():
    if missing_count > 0:
        print(f"  {col}: {missing_count} ({missing_count/len(df)*100:.2f}%)")
    else:
        print(f"  {col}: No missing values ✅")

# Check for empty strings
print(f"\n📝 Empty strings:")
for col in df.columns:
    if df[col].dtype == 'object':
        empty_count = (df[col].astype(str).str.strip() == '').sum()
        if empty_count > 0:
            print(f"  {col}: {empty_count} empty strings")
        else:
            print(f"  {col}: No empty strings ✅")

# Check for duplicates
duplicate_count = df.duplicated().sum()
print(f"\n🔄 Duplicate rows: {duplicate_count}")

# Sentiment label distribution
print(f"\n📊 Sentiment Distribution:")
sentiment_counts = df['Sentiment'].value_counts()
print(sentiment_counts)

print(f"\nPercentages:")
sentiment_percentages = df['Sentiment'].value_counts(normalize=True) * 100
for sentiment, percentage in sentiment_percentages.items():
    print(f"  {sentiment}: {percentage:.2f}%")

# Visualize sentiment distribution
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

# Bar plot
sentiment_counts.plot(kind='bar', ax=ax1, color=['#d62728', '#ff7f0e', '#2ca02c'])
ax1.set_title('Sentiment Distribution (Count)', fontsize=14, fontweight='bold')
ax1.set_xlabel('Sentiment')
ax1.set_ylabel('Count')
ax1.tick_params(axis='x', rotation=45)

# Pie chart
ax2.pie(sentiment_counts.values, labels=sentiment_counts.index, autopct='%1.1f%%', 
        colors=['#d62728', '#ff7f0e', '#2ca02c'], startangle=90)
ax2.set_title('Sentiment Distribution (Percentage)', fontsize=14, fontweight='bold')

plt.tight_layout()
plt.show()

print(f"\n✅ Data exploration completed!")

# Text cleaning functions for financial data
def clean_financial_text(text):
    """
    Comprehensive text cleaning function optimized for financial text data
    """
    if pd.isna(text) or text is None:
        return ""
    
    # Convert to string if not already
    text = str(text)
    
    # Decode HTML entities
    text = unescape(text)
    
    # Remove HTML tags
    text = re.sub(r'<[^>]+>', '', text)
    
    # Normalize unicode characters
    text = normalize('NFKD', text)
    
    # Remove control characters but keep basic punctuation
    text = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F-\x9F]', ' ', text)
    
    # Fix common encoding issues
    text = text.replace('â€™', "'")
    text = text.replace('â€œ', '"')
    text = text.replace('â€�', '"')
    text = text.replace('â€"', '-')
    text = text.replace('â€"', '--')
    
    # Clean up multiple spaces, tabs, and newlines
    text = re.sub(r'\s+', ' ', text)
    
    # Remove leading/trailing whitespace
    text = text.strip()
    
    return text

def validate_financial_text(text, min_length=10):
    """
    Validate if text meets requirements for financial sentiment analysis
    """
    if pd.isna(text) or text is None:
        return False
    
    text = str(text).strip()
    
    # Check minimum length
    if len(text) < min_length:
        return False
    
    # Check if text contains at least some alphabetic characters
    if not re.search(r'[a-zA-Z]', text):
        return False
    
    # Check for meaningful content (not just numbers/symbols)
    alpha_ratio = len(re.findall(r'[a-zA-Z]', text)) / len(text)
    if alpha_ratio < 0.3:  # At least 30% alphabetic characters
        return False
    
    return True

print("✅ Text cleaning functions defined!")
print("📋 Functions available:")
print("  - clean_financial_text(): Comprehensive text cleaning")
print("  - validate_financial_text(): Text quality validation")

# Apply comprehensive data cleaning
print("🧹 Starting data cleaning process...")
df_clean = df.copy()
initial_rows = len(df_clean)

print(f"Initial dataset size: {initial_rows:,} rows")

# Clean the Sentence column
print("\n🔧 Cleaning text data...")
df_clean['Sentence_cleaned'] = df_clean['Sentence'].apply(clean_financial_text)

# Remove rows with invalid text
print("🔍 Filtering invalid entries...")
valid_mask = df_clean['Sentence_cleaned'].apply(validate_financial_text)
invalid_count = (~valid_mask).sum()
df_clean = df_clean[valid_mask].copy()

print(f"Removed {invalid_count:,} invalid entries ({invalid_count/initial_rows*100:.1f}%)")
print(f"Remaining rows: {len(df_clean):,}")

# Remove exact duplicates based on cleaned text
print("\n🔄 Removing duplicates...")
before_dedup = len(df_clean)
df_clean = df_clean.drop_duplicates(subset=['Sentence_cleaned'], keep='first')
duplicates_removed = before_dedup - len(df_clean)

print(f"Removed {duplicates_removed:,} duplicate entries")
print(f"Final dataset size: {len(df_clean):,} rows")

# Calculate text statistics
df_clean['text_length'] = df_clean['Sentence_cleaned'].str.len()
df_clean['word_count'] = df_clean['Sentence_cleaned'].str.split().str.len()

# Show cleaning examples
print(f"\n✨ Text cleaning examples:")
for i in range(min(3, len(df_clean))):
    idx = df_clean.index[i]
    original = df.loc[idx, 'Sentence']
    cleaned = df_clean.loc[idx, 'Sentence_cleaned']
    
    print(f"\nExample {i+1}:")
    print(f"Original:  {original[:80]}...")
    print(f"Cleaned:   {cleaned[:80]}...")
    print(f"Sentiment: {df_clean.loc[idx, 'Sentiment']}")

# Show text statistics
print(f"\n📊 Text Statistics After Cleaning:")
print(f"Average text length: {df_clean['text_length'].mean():.1f} characters")
print(f"Average word count: {df_clean['word_count'].mean():.1f} words")
print(f"Text length range: {df_clean['text_length'].min()} - {df_clean['text_length'].max()} characters")

# Update sentiment distribution after cleaning
print(f"\n📈 Sentiment distribution after cleaning:")
sentiment_after_cleaning = df_clean['Sentiment'].value_counts()
print(sentiment_after_cleaning)

# Reset index for clean dataset
df_clean = df_clean.reset_index(drop=True)
print(f"\n✅ Data cleaning completed successfully!")
print(f"Ready for training: {len(df_clean):,} clean samples")

# Label encoding for sentiment analysis
print("🏷️ Encoding sentiment labels...")

# Check unique sentiment values
unique_sentiments = df_clean['Sentiment'].unique()
print(f"Unique sentiment labels: {unique_sentiments}")

# Create label mapping (standard for financial sentiment analysis)
label_mapping = {
    'negative': 0,
    'neutral': 1, 
    'positive': 2
}

def normalize_sentiment_label(sentiment):
    """Normalize sentiment labels to handle case variations"""
    if pd.isna(sentiment):
        return sentiment
    
    sentiment_str = str(sentiment).lower().strip()
    
    # Map variations to standard labels
    if sentiment_str in ['negative', 'neg', 'bad', 'bearish', '-1']:
        return 'negative'
    elif sentiment_str in ['neutral', 'neu', 'none', 'neut', 'hold', '0']:
        return 'neutral'
    elif sentiment_str in ['positive', 'pos', 'good', 'bullish', 'buy', '1']:
        return 'positive'
    else:
        return sentiment_str

# Normalize sentiment labels
df_clean['Sentiment_normalized'] = df_clean['Sentiment'].apply(normalize_sentiment_label)

# Check for any unmapped labels
unmapped_labels = df_clean[~df_clean['Sentiment_normalized'].isin(label_mapping.keys())]
if len(unmapped_labels) > 0:
    print(f"⚠️ Found {len(unmapped_labels)} unmapped labels:")
    print(unmapped_labels['Sentiment_normalized'].value_counts())
    
    # Remove rows with unmapped labels
    df_clean = df_clean[df_clean['Sentiment_normalized'].isin(label_mapping.keys())].copy()
    print(f"Removed unmapped labels. Remaining samples: {len(df_clean):,}")

# Apply label encoding
df_clean['label'] = df_clean['Sentiment_normalized'].map(label_mapping)

# Create reverse mapping for reference
reverse_label_mapping = {v: k for k, v in label_mapping.items()}

# Validate label encoding
print(f"\n✅ Label encoding validation:")
print(f"Label mapping: {label_mapping}")
print(f"Reverse mapping: {reverse_label_mapping}")

# Check final label distribution
label_distribution = df_clean['label'].value_counts().sort_index()
print(f"\nFinal label distribution:")
for label, count in label_distribution.items():
    sentiment_name = reverse_label_mapping[label]
    percentage = (count / len(df_clean)) * 100
    print(f"  {label} ({sentiment_name}): {count:,} samples ({percentage:.2f}%)")

# Verify no missing labels
missing_labels = df_clean['label'].isna().sum()
if missing_labels > 0:
    print(f"⚠️ Found {missing_labels} missing labels - removing them")
    df_clean = df_clean.dropna(subset=['label']).copy()

print(f"\n🎯 Label encoding completed!")
print(f"Dataset ready for splitting: {len(df_clean):,} labeled samples")

# Stratified data splitting for balanced training
print("🔄 Splitting data into train/validation/test sets...")

# Define split ratios
train_ratio = 0.7    # 70% for training
val_ratio = 0.15     # 15% for validation  
test_ratio = 0.15    # 15% for testing

print(f"Split ratios: Train={train_ratio:.0%}, Validation={val_ratio:.0%}, Test={test_ratio:.0%}")

# Prepare data for splitting
X = df_clean[['Sentence_cleaned']].copy()
y = df_clean['label'].copy()

# First split: separate test set (stratified)
X_temp, X_test, y_temp, y_test = train_test_split(
    X, y, 
    test_size=test_ratio, 
    stratify=y, 
    random_state=42
)

# Second split: separate train and validation (stratified)
val_ratio_adjusted = val_ratio / (train_ratio + val_ratio)
X_train, X_val, y_train, y_val = train_test_split(
    X_temp, y_temp,
    test_size=val_ratio_adjusted,
    stratify=y_temp,
    random_state=42
)

# Verify splits
print(f"\n📊 Data split sizes:")
print(f"Train: {len(X_train):,} samples ({len(X_train)/len(df_clean):.1%})")
print(f"Validation: {len(X_val):,} samples ({len(X_val)/len(df_clean):.1%})")
print(f"Test: {len(X_test):,} samples ({len(X_test)/len(df_clean):.1%})")
print(f"Total: {len(X_train) + len(X_val) + len(X_test):,} samples")

# Check label distribution in each split
def check_split_distribution(y_split, split_name):
    """Check and display label distribution for a split"""
    distribution = y_split.value_counts().sort_index()
    percentages = y_split.value_counts(normalize=True).sort_index() * 100
    
    print(f"\n{split_name} label distribution:")
    for label in sorted(distribution.index):
        sentiment_name = reverse_label_mapping[label]
        count = distribution[label]
        percentage = percentages[label]
        print(f"  {label} ({sentiment_name}): {count:,} ({percentage:.1f}%)")
    
    return distribution

train_dist = check_split_distribution(y_train, "Train")
val_dist = check_split_distribution(y_val, "Validation")
test_dist = check_split_distribution(y_test, "Test")

# Create final datasets
train_data = pd.DataFrame({
    'text': X_train['Sentence_cleaned'].values,
    'label': y_train.values
}).reset_index(drop=True)

val_data = pd.DataFrame({
    'text': X_val['Sentence_cleaned'].values,
    'label': y_val.values  
}).reset_index(drop=True)

test_data = pd.DataFrame({
    'text': X_test['Sentence_cleaned'].values,
    'label': y_test.values
}).reset_index(drop=True)

# Check for data leakage
print(f"\n🔍 Checking for data leakage...")
train_texts = set(train_data['text'])
val_texts = set(val_data['text'])
test_texts = set(test_data['text'])

train_val_overlap = len(train_texts.intersection(val_texts))
train_test_overlap = len(train_texts.intersection(test_texts))
val_test_overlap = len(val_texts.intersection(test_texts))

print(f"Train-Validation overlap: {train_val_overlap} texts")
print(f"Train-Test overlap: {train_test_overlap} texts")
print(f"Validation-Test overlap: {val_test_overlap} texts")

if train_val_overlap == 0 and train_test_overlap == 0 and val_test_overlap == 0:
    print("✅ No data leakage detected!")
else:
    print("⚠️ Data leakage detected!")

print(f"\n✅ Data splitting completed successfully!")
print(f"Ready for FinBERT tokenization")

# Load FinBERT tokenizer and analyze sequence lengths
print("🤖 Loading FinBERT tokenizer...")

MODEL_NAME = "ProsusAI/finbert"
MAX_LENGTH = 512  # Standard for FinBERT

try:
    tokenizer = AutoTokenizer.from_pretrained(MODEL_NAME)
    print(f"✅ FinBERT tokenizer loaded successfully!")
    print(f"Model: {MODEL_NAME}")
    print(f"Vocabulary size: {tokenizer.vocab_size:,}")
    print(f"Max model length: {tokenizer.model_max_length}")
except Exception as e:
    print(f"❌ Error loading FinBERT tokenizer: {e}")
    print("🔄 Falling back to BERT tokenizer...")
    MODEL_NAME = "bert-base-uncased"
    tokenizer = AutoTokenizer.from_pretrained(MODEL_NAME)

# Analyze token lengths to determine optimal max_length
print(f"\n📏 Analyzing token lengths for optimal sequence length...")

def analyze_token_lengths(texts, sample_size=1000):
    """Analyze token lengths in a sample of texts"""
    
    if len(texts) > sample_size:
        sample_indices = np.random.choice(len(texts), sample_size, replace=False)
        sample_texts = [texts.iloc[i] for i in sample_indices]
    else:
        sample_texts = texts.tolist()
    
    token_lengths = []
    
    print(f"Analyzing {len(sample_texts)} samples...")
    for text in tqdm(sample_texts, desc="Tokenizing"):
        tokens = tokenizer(text, truncation=False, add_special_tokens=True)
        token_lengths.append(len(tokens['input_ids']))
    
    return np.array(token_lengths)

# Analyze token lengths across all data
print("\nAnalyzing token lengths across dataset...")
all_texts = pd.concat([train_data['text'], val_data['text'], test_data['text']])
token_lengths = analyze_token_lengths(all_texts, sample_size=2000)

# Calculate statistics
stats = {
    'mean': np.mean(token_lengths),
    'median': np.median(token_lengths),
    'std': np.std(token_lengths),
    'min': np.min(token_lengths),
    'max': np.max(token_lengths),
    'percentile_95': np.percentile(token_lengths, 95),
    'percentile_99': np.percentile(token_lengths, 99)
}

print(f"\n📊 Token Length Statistics:")
print(f"  Mean: {stats['mean']:.1f} tokens")
print(f"  Median: {stats['median']:.1f} tokens")
print(f"  95th percentile: {stats['percentile_95']:.1f} tokens")
print(f"  99th percentile: {stats['percentile_99']:.1f} tokens")
print(f"  Max: {stats['max']} tokens")

# Determine optimal max_length
recommended_max_length = int(np.percentile(token_lengths, 95))
max_length_options = [128, 256, 512]
optimal_max_length = min([x for x in max_length_options if x >= recommended_max_length], default=512)

print(f"\nRecommended max_length: {recommended_max_length}")
print(f"Chosen max_length: {optimal_max_length}")

# Calculate truncation impact
truncated_samples = (token_lengths > optimal_max_length).sum()
truncation_percentage = (truncated_samples / len(token_lengths)) * 100

print(f"Samples that will be truncated: {truncated_samples} ({truncation_percentage:.2f}%)")

# Visualize token length distribution
plt.figure(figsize=(12, 6))

plt.subplot(1, 2, 1)
plt.hist(token_lengths, bins=50, alpha=0.7, edgecolor='black')
plt.axvline(optimal_max_length, color='red', linestyle='--', 
            label=f'max_length={optimal_max_length}')
plt.axvline(np.mean(token_lengths), color='green', linestyle='--', 
            label=f'Mean={np.mean(token_lengths):.1f}')
plt.title('Token Length Distribution')
plt.xlabel('Token Length')
plt.ylabel('Frequency')
plt.legend()

plt.subplot(1, 2, 2)
sorted_lengths = np.sort(token_lengths)
cumulative_pct = np.arange(1, len(sorted_lengths) + 1) / len(sorted_lengths) * 100
plt.plot(sorted_lengths, cumulative_pct)
plt.axvline(optimal_max_length, color='red', linestyle='--', 
            label=f'max_length={optimal_max_length}')
plt.title('Cumulative Token Length Distribution')
plt.xlabel('Token Length')
plt.ylabel('Cumulative Percentage')
plt.legend()
plt.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

print(f"\n✅ Token analysis completed!")
print(f"Proceeding with max_length={optimal_max_length} for tokenization")

# Create HuggingFace datasets with tokenization
print("🔄 Creating tokenized HuggingFace datasets...")

def tokenize_function(examples):
    """Tokenize text examples for FinBERT training"""
    return tokenizer(
        examples['text'],
        truncation=True,
        padding=False,  # Will pad dynamically during training
        max_length=optimal_max_length,
        return_tensors=None
    )

# Create HuggingFace datasets
print("Creating datasets...")
train_dataset = Dataset.from_pandas(train_data)
val_dataset = Dataset.from_pandas(val_data)
test_dataset = Dataset.from_pandas(test_data)

# Apply tokenization
print("Tokenizing training set...")
train_dataset = train_dataset.map(
    tokenize_function, 
    batched=True, 
    desc="Tokenizing train"
)

print("Tokenizing validation set...")
val_dataset = val_dataset.map(
    tokenize_function, 
    batched=True, 
    desc="Tokenizing validation"
)

print("Tokenizing test set...")
test_dataset = test_dataset.map(
    tokenize_function, 
    batched=True, 
    desc="Tokenizing test"
)

# Create DatasetDict
tokenized_datasets = DatasetDict({
    'train': train_dataset,
    'validation': val_dataset,
    'test': test_dataset
})

# Remove text column as it's no longer needed
tokenized_datasets = tokenized_datasets.remove_columns(['text'])

print(f"\n✅ Tokenized datasets created:")
print(f"Dataset structure: {tokenized_datasets}")

# Verify tokenization
print(f"\n🔍 Dataset verification:")
for split_name, dataset in tokenized_datasets.items():
    print(f"\n{split_name.capitalize()} dataset:")
    print(f"  Size: {len(dataset):,}")
    print(f"  Features: {dataset.features}")
    
    # Check a sample
    sample = dataset[0]
    print(f"  Sample input_ids length: {len(sample['input_ids'])}")
    print(f"  Sample attention_mask length: {len(sample['attention_mask'])}")
    print(f"  Sample label: {sample['label']} ({reverse_label_mapping[sample['label']]})")

# Show example of tokenized data
print(f"\n📝 Tokenization example:")
sample = tokenized_datasets['train'][0]
input_ids = sample['input_ids']
attention_mask = sample['attention_mask']
label = sample['label']

print(f"Label: {label} ({reverse_label_mapping[label]})")
print(f"Input IDs (first 20): {input_ids[:20]}")
print(f"Attention mask (first 20): {attention_mask[:20]}")

# Decode to verify
decoded_text = tokenizer.decode(input_ids, skip_special_tokens=True)
print(f"Decoded text: {decoded_text[:100]}...")

# Actual vs padding tokens
actual_tokens = sum(attention_mask)
padding_tokens = len(attention_mask) - actual_tokens
print(f"Actual tokens: {actual_tokens}, Padding tokens: {padding_tokens}")

print(f"\n✅ Tokenization completed successfully!")
print(f"Datasets ready for FinBERT training")

# Load and configure FinBERT model for sequence classification
print("🏗️ Loading FinBERT model for sentiment classification...")

# Model configuration
num_labels = 3  # negative, neutral, positive
id2label = reverse_label_mapping
label2id = label_mapping

try:
    # Load model configuration
    config = AutoConfig.from_pretrained(
        MODEL_NAME,
        num_labels=num_labels,
        id2label=id2label,
        label2id=label2id,
        finetuning_task="sentiment-analysis"
    )
    
    # Load pre-trained model
    model = AutoModelForSequenceClassification.from_pretrained(
        MODEL_NAME,
        config=config,
        ignore_mismatched_sizes=True  # Handle size mismatches for classification head
    )
    
    print(f"✅ FinBERT model loaded successfully!")
    print(f"Model: {MODEL_NAME}")
    print(f"Number of labels: {num_labels}")
    print(f"Label mapping: {label2id}")
    
except Exception as e:
    print(f"❌ Error loading FinBERT model: {e}")
    print("🔄 This might happen if the model doesn't exist - check MODEL_NAME")
    raise

# Move model to GPU if available
model = model.to(device)
print(f"📱 Model moved to: {device}")

# Display model information
print(f"\n📊 Model Information:")
total_params = sum(p.numel() for p in model.parameters())
trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)

print(f"  Total parameters: {total_params:,}")
print(f"  Trainable parameters: {trainable_params:,}")
print(f"  Model size: ~{total_params * 4 / 1024**2:.1f} MB")

# Check model architecture
print(f"\n🏗️ Model Architecture:")
print(f"  Base model: {model.bert.__class__.__name__}")
print(f"  Classification head: {model.classifier}")
print(f"  Hidden size: {model.config.hidden_size}")
print(f"  Number of attention heads: {model.config.num_attention_heads}")
print(f"  Number of hidden layers: {model.config.num_hidden_layers}")

print(f"\n✅ Model configuration completed!")

# Configure training arguments and data collator
print("⚙️ Setting up training configuration...")

# Training hyperparameters (optimized for FinBERT sentiment analysis)
BATCH_SIZE = 16 if torch.cuda.is_available() else 8
LEARNING_RATE = 2e-5  # Standard learning rate for FinBERT fine-tuning
NUM_EPOCHS = 3  # Typically 2-4 epochs for fine-tuning
WARMUP_STEPS = 500
WEIGHT_DECAY = 0.01
SAVE_STRATEGY = "epoch"
EVALUATION_STRATEGY = "epoch"
LOGGING_STEPS = 100

# Output directory for model checkpoints
OUTPUT_DIR = "./finbert-sentiment-finetuned"

# Training arguments
training_args = TrainingArguments(
    output_dir=OUTPUT_DIR,
    num_train_epochs=NUM_EPOCHS,
    per_device_train_batch_size=BATCH_SIZE,
    per_device_eval_batch_size=BATCH_SIZE,
    warmup_steps=WARMUP_STEPS,
    weight_decay=WEIGHT_DECAY,
    learning_rate=LEARNING_RATE,
    logging_dir='./logs',
    logging_steps=LOGGING_STEPS,
    evaluation_strategy=EVALUATION_STRATEGY,
    save_strategy=SAVE_STRATEGY,
    save_total_limit=2,  # Keep only best 2 checkpoints
    load_best_model_at_end=True,
    metric_for_best_model="eval_accuracy",
    greater_is_better=True,
    push_to_hub=False,  # Set to True if you want to push to HuggingFace Hub
    report_to="none",  # Disable wandb logging for simplicity
    dataloader_pin_memory=False,
    fp16=torch.cuda.is_available(),  # Enable mixed precision if GPU available
    gradient_checkpointing=True,  # Save memory
    dataloader_num_workers=0,  # Set to 0 for Colab compatibility
    remove_unused_columns=False,
)

print(f"✅ Training arguments configured:")
print(f"  Batch size: {BATCH_SIZE}")
print(f"  Learning rate: {LEARNING_RATE}")
print(f"  Number of epochs: {NUM_EPOCHS}")
print(f"  Warmup steps: {WARMUP_STEPS}")
print(f"  Output directory: {OUTPUT_DIR}")
print(f"  Mixed precision (FP16): {training_args.fp16}")

# Data collator for dynamic padding
data_collator = DataCollatorWithPadding(
    tokenizer=tokenizer,
    padding=True,
    max_length=optimal_max_length,
    pad_to_multiple_of=8 if training_args.fp16 else None,
)

print(f"✅ Data collator configured for dynamic padding")

# Define evaluation metrics
def compute_metrics(eval_pred):
    """Compute accuracy and F1 scores for evaluation"""
    predictions, labels = eval_pred
    predictions = np.argmax(predictions, axis=1)
    
    # Calculate metrics
    accuracy = accuracy_score(labels, predictions)
    
    # Classification report
    report = classification_report(
        labels, 
        predictions, 
        target_names=[reverse_label_mapping[i] for i in sorted(reverse_label_mapping.keys())],
        output_dict=True,
        zero_division=0
    )
    
    return {
        "accuracy": accuracy,
        "f1_macro": report["macro avg"]["f1-score"],
        "f1_weighted": report["weighted avg"]["f1-score"],
        "precision_macro": report["macro avg"]["precision"],
        "recall_macro": report["macro avg"]["recall"]
    }

print(f"✅ Evaluation metrics function defined")
print(f"Metrics: accuracy, f1_macro, f1_weighted, precision_macro, recall_macro")

print(f"\n🎯 Training setup completed!")
print(f"Ready to initialize Trainer and begin fine-tuning")

# Initialize Trainer and start fine-tuning
print("🏋️ Initializing FinBERT Trainer...")

# Initialize the Trainer
trainer = Trainer(
    model=model,
    args=training_args,
    train_dataset=tokenized_datasets['train'],
    eval_dataset=tokenized_datasets['validation'],
    tokenizer=tokenizer,
    data_collator=data_collator,
    compute_metrics=compute_metrics,
    callbacks=[EarlyStoppingCallback(early_stopping_patience=2)]
)

print(f"✅ Trainer initialized successfully!")
print(f"📊 Training dataset size: {len(tokenized_datasets['train']):,}")
print(f"📊 Validation dataset size: {len(tokenized_datasets['validation']):,}")

# Calculate estimated training time
steps_per_epoch = len(tokenized_datasets['train']) // BATCH_SIZE
total_steps = steps_per_epoch * NUM_EPOCHS
estimated_time_hours = total_steps * 2 / 3600  # Rough estimate: 2 seconds per step

print(f"\n⏱️ Training Estimates:")
print(f"  Steps per epoch: {steps_per_epoch}")
print(f"  Total training steps: {total_steps}")
print(f"  Estimated training time: {estimated_time_hours:.1f} hours")

print(f"\n🚀 Starting FinBERT fine-tuning...")
print(f"📈 Training progress will be displayed below:")

# Record start time
start_time = time.time()

# Start training
try:
    trainer.train()
    
    # Calculate actual training time
    end_time = time.time()
    training_time = end_time - start_time
    
    print(f"\n🎉 Training completed successfully!")
    print(f"⏱️ Total training time: {training_time/3600:.2f} hours ({training_time/60:.1f} minutes)")
    
except Exception as e:
    print(f"❌ Training failed with error: {e}")
    raise

# Get training history
train_history = trainer.state.log_history

print(f"\n📈 Training Summary:")
print(f"  Total epochs completed: {trainer.state.epoch}")
print(f"  Total steps: {trainer.state.global_step}")
print(f"  Best model metric: {trainer.state.best_metric:.4f}")

# Display final metrics
if train_history:
    final_train_loss = None
    final_eval_metrics = None
    
    for log in reversed(train_history):
        if 'train_loss' in log and final_train_loss is None:
            final_train_loss = log['train_loss']
        if 'eval_accuracy' in log and final_eval_metrics is None:
            final_eval_metrics = {k: v for k, v in log.items() if k.startswith('eval_')}
        
        if final_train_loss is not None and final_eval_metrics is not None:
            break
    
    if final_train_loss:
        print(f"  Final training loss: {final_train_loss:.4f}")
    
    if final_eval_metrics:
        print(f"  Final validation metrics:")
        for metric, value in final_eval_metrics.items():
            print(f"    {metric}: {value:.4f}")

print(f"\n✅ FinBERT fine-tuning completed!")

# Visualize training progress and metrics
print("📊 Analyzing training progress...")

# Extract training metrics from log history
train_losses = []
eval_losses = []
eval_accuracies = []
eval_f1_scores = []
steps = []
epochs = []

for log in train_history:
    if 'train_loss' in log:
        train_losses.append(log['train_loss'])
        steps.append(log['step'])
    
    if 'eval_loss' in log:
        eval_losses.append(log['eval_loss'])
        eval_accuracies.append(log['eval_accuracy'])
        eval_f1_scores.append(log['eval_f1_macro'])
        epochs.append(log['epoch'])

# Create comprehensive training visualization
fig, axes = plt.subplots(2, 2, figsize=(16, 12))

# Training Loss
axes[0,0].plot(steps, train_losses, 'b-', label='Training Loss', linewidth=2)
axes[0,0].set_title('Training Loss Over Steps', fontsize=14, fontweight='bold')
axes[0,0].set_xlabel('Steps')
axes[0,0].set_ylabel('Loss')
axes[0,0].legend()
axes[0,0].grid(True, alpha=0.3)

# Validation Loss
if eval_losses:
    axes[0,1].plot(epochs, eval_losses, 'r-', label='Validation Loss', linewidth=2, marker='o')
    axes[0,1].set_title('Validation Loss Over Epochs', fontsize=14, fontweight='bold')
    axes[0,1].set_xlabel('Epoch')
    axes[0,1].set_ylabel('Loss')
    axes[0,1].legend()
    axes[0,1].grid(True, alpha=0.3)

# Validation Accuracy
if eval_accuracies:
    axes[1,0].plot(epochs, eval_accuracies, 'g-', label='Validation Accuracy', linewidth=2, marker='o')
    axes[1,0].set_title('Validation Accuracy Over Epochs', fontsize=14, fontweight='bold')
    axes[1,0].set_xlabel('Epoch')
    axes[1,0].set_ylabel('Accuracy')
    axes[1,0].set_ylim(0, 1)
    axes[1,0].legend()
    axes[1,0].grid(True, alpha=0.3)

# Validation F1 Score
if eval_f1_scores:
    axes[1,1].plot(epochs, eval_f1_scores, 'purple', label='Validation F1 (Macro)', linewidth=2, marker='o')
    axes[1,1].set_title('Validation F1 Score Over Epochs', fontsize=14, fontweight='bold')
    axes[1,1].set_xlabel('Epoch')
    axes[1,1].set_ylabel('F1 Score')
    axes[1,1].set_ylim(0, 1)
    axes[1,1].legend()
    axes[1,1].grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

# Training summary statistics
if train_losses:
    print(f"\n📈 Training Progress Summary:")
    print(f"  Initial training loss: {train_losses[0]:.4f}")
    print(f"  Final training loss: {train_losses[-1]:.4f}")
    print(f"  Loss reduction: {((train_losses[0] - train_losses[-1]) / train_losses[0] * 100):.1f}%")

if eval_accuracies:
    print(f"\n🎯 Validation Performance:")
    print(f"  Best accuracy: {max(eval_accuracies):.4f}")
    print(f"  Final accuracy: {eval_accuracies[-1]:.4f}")
    print(f"  Best F1 score: {max(eval_f1_scores):.4f}")
    print(f"  Final F1 score: {eval_f1_scores[-1]:.4f}")

# Check for overfitting signs
if len(eval_losses) >= 2:
    if eval_losses[-1] > eval_losses[-2]:
        print(f"\n⚠️ Warning: Validation loss increased in final epoch - possible overfitting")
    else:
        print(f"\n✅ Validation loss decreased or stabilized - good generalization")

print(f"\n🎉 Training analysis completed!")

# Evaluate the fine-tuned model on test set
print("🎯 Evaluating fine-tuned FinBERT on test set...")

# Evaluate on test set
test_results = trainer.evaluate(eval_dataset=tokenized_datasets['test'])

print(f"\n📊 Test Set Results:")
for metric, value in test_results.items():
    if metric.startswith('eval_'):
        metric_name = metric.replace('eval_', '').replace('_', ' ').title()
        print(f"  {metric_name}: {value:.4f}")

# Get predictions for detailed analysis
print(f"\n🔍 Generating detailed predictions...")

# Make predictions
predictions = trainer.predict(tokenized_datasets['test'])
y_pred = np.argmax(predictions.predictions, axis=1)
y_true = predictions.label_ids

# Calculate comprehensive metrics
test_accuracy = accuracy_score(y_true, y_pred)
test_report = classification_report(
    y_true, 
    y_pred, 
    target_names=[reverse_label_mapping[i] for i in sorted(reverse_label_mapping.keys())],
    output_dict=True,
    zero_division=0
)

print(f"\n📈 Detailed Test Set Performance:")
print(f"  Overall Accuracy: {test_accuracy:.4f}")
print(f"  Macro F1 Score: {test_report['macro avg']['f1-score']:.4f}")
print(f"  Weighted F1 Score: {test_report['weighted avg']['f1-score']:.4f}")

print(f"\n📋 Per-Class Performance:")
for label_num in sorted(reverse_label_mapping.keys()):
    label_name = reverse_label_mapping[label_num]
    metrics = test_report[label_name]
    print(f"  {label_name.capitalize()}:")
    print(f"    Precision: {metrics['precision']:.4f}")
    print(f"    Recall: {metrics['recall']:.4f}")
    print(f"    F1-Score: {metrics['f1-score']:.4f}")
    print(f"    Support: {int(metrics['support'])}")

# Confusion Matrix
cm = confusion_matrix(y_true, y_pred)
print(f"\n🔢 Confusion Matrix:")
print(f"    Predicted:")
print(f"         Neg  Neu  Pos")
for i, label in enumerate(['Neg', 'Neu', 'Pos']):
    print(f"True {label}: {cm[i]}")

# Visualize confusion matrix
plt.figure(figsize=(10, 8))
sns.heatmap(
    cm, 
    annot=True, 
    fmt='d', 
    cmap='Blues',
    xticklabels=[reverse_label_mapping[i] for i in sorted(reverse_label_mapping.keys())],
    yticklabels=[reverse_label_mapping[i] for i in sorted(reverse_label_mapping.keys())]
)
plt.title('Confusion Matrix - FinBERT Sentiment Classification', fontsize=16, fontweight='bold')
plt.xlabel('Predicted Sentiment', fontsize=12)
plt.ylabel('True Sentiment', fontsize=12)
plt.show()

# Calculate and show prediction confidence scores
prediction_probs = torch.nn.functional.softmax(torch.tensor(predictions.predictions), dim=1)
confidence_scores = torch.max(prediction_probs, dim=1)[0]

print(f"\n🎯 Prediction Confidence Analysis:")
print(f"  Average confidence: {confidence_scores.mean():.4f}")
print(f"  Median confidence: {confidence_scores.median():.4f}")
print(f"  Min confidence: {confidence_scores.min():.4f}")
print(f"  Max confidence: {confidence_scores.max():.4f}")

# Show some example predictions
print(f"\n📝 Sample Predictions:")
test_texts = test_data['text'].tolist()
sample_indices = np.random.choice(len(y_pred), 5, replace=False)

for i, idx in enumerate(sample_indices):
    text = test_texts[idx]
    true_label = reverse_label_mapping[y_true[idx]]
    pred_label = reverse_label_mapping[y_pred[idx]]
    confidence = confidence_scores[idx].item()
    
    print(f"\nExample {i+1}:")
    print(f"  Text: {text[:100]}...")
    print(f"  True sentiment: {true_label}")
    print(f"  Predicted sentiment: {pred_label}")
    print(f"  Confidence: {confidence:.4f}")
    print(f"  Correct: {'✅' if true_label == pred_label else '❌'}")

print(f"\n✅ Model evaluation completed!")

# Save the fine-tuned model and tokenizer
print("💾 Saving fine-tuned FinBERT model...")

# Save the best model
final_model_dir = "./finbert-sentiment-final"
trainer.save_model(final_model_dir)
tokenizer.save_pretrained(final_model_dir)

print(f"✅ Model saved to: {final_model_dir}")

# Create model card and configuration
model_info = {
    "model_name": "FinBERT Fine-tuned for Financial Sentiment Analysis",
    "base_model": MODEL_NAME,
    "task": "Financial Sentiment Classification",
    "dataset_size": len(df_clean),
    "train_size": len(tokenized_datasets['train']),
    "val_size": len(tokenized_datasets['validation']),
    "test_size": len(tokenized_datasets['test']),
    "num_labels": num_labels,
    "label_mapping": label_mapping,
    "max_length": optimal_max_length,
    "training_time_hours": training_time/3600,
    "test_accuracy": test_accuracy,
    "test_f1_macro": test_report['macro avg']['f1-score'],
    "test_f1_weighted": test_report['weighted avg']['f1-score'],
    "training_params": {
        "learning_rate": LEARNING_RATE,
        "batch_size": BATCH_SIZE,
        "num_epochs": NUM_EPOCHS,
        "warmup_steps": WARMUP_STEPS,
        "weight_decay": WEIGHT_DECAY
    },
    "timestamp": datetime.now().isoformat()
}

# Save model information
with open(f"{final_model_dir}/model_info.json", 'w') as f:
    json.dump(model_info, f, indent=2)

print(f"✅ Model information saved")

# Download model files for local use
print(f"\n📁 Model files ready for download:")
print(f"  Location: {final_model_dir}/")
print(f"  Files: config.json, pytorch_model.bin, tokenizer files, model_info.json")

# Create README for the model
readme_content = f"""# FinBERT Financial Sentiment Analysis Model

## Model Description
This is a fine-tuned version of FinBERT (`{MODEL_NAME}`) for financial sentiment analysis.

## Model Performance
- **Test Accuracy**: {test_accuracy:.4f}
- **Test F1 (Macro)**: {test_report['macro avg']['f1-score']:.4f}
- **Test F1 (Weighted)**: {test_report['weighted avg']['f1-score']:.4f}

## Dataset
- **Total Samples**: {len(df_clean):,}
- **Training Samples**: {len(tokenized_datasets['train']):,}
- **Validation Samples**: {len(tokenized_datasets['validation']):,}
- **Test Samples**: {len(tokenized_datasets['test']):,}

## Label Mapping
- 0: negative
- 1: neutral
- 2: positive

## Usage

```python
from transformers import AutoTokenizer, AutoModelForSequenceClassification
import torch

# Load model and tokenizer
tokenizer = AutoTokenizer.from_pretrained("./finbert-sentiment-final")
model = AutoModelForSequenceClassification.from_pretrained("./finbert-sentiment-final")

# Example prediction
text = "The company reported strong quarterly earnings."
inputs = tokenizer(text, return_tensors="pt", truncation=True, padding=True, max_length={optimal_max_length})

with torch.no_grad():
    outputs = model(**inputs)
    predictions = torch.nn.functional.softmax(outputs.logits, dim=-1)
    predicted_class = torch.argmax(predictions, dim=-1).item()

# Map prediction to sentiment
sentiment_map = {{0: "negative", 1: "neutral", 2: "positive"}}
predicted_sentiment = sentiment_map[predicted_class]
confidence = predictions[0][predicted_class].item()

print(f"Sentiment: {{predicted_sentiment}} (confidence: {{confidence:.4f}})")
```

## Training Details
- **Base Model**: {MODEL_NAME}
- **Training Time**: {training_time/3600:.2f} hours
- **Learning Rate**: {LEARNING_RATE}
- **Batch Size**: {BATCH_SIZE}
- **Epochs**: {NUM_EPOCHS}

## Fine-tuning Date
{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""

with open(f"{final_model_dir}/README.md", 'w') as f:
    f.write(readme_content)

print(f"✅ README.md created")

# Final summary
print(f"\n{'='*60}")
print(f"🎉 FINBERT TRAINING COMPLETED SUCCESSFULLY!")
print(f"{'='*60}")

print(f"\n📊 Final Results Summary:")
print(f"  Dataset processed: {len(df_clean):,} samples")
print(f"  Training time: {training_time/3600:.2f} hours")
print(f"  Test accuracy: {test_accuracy:.4f}")
print(f"  Test F1 (macro): {test_report['macro avg']['f1-score']:.4f}")
print(f"  Model saved to: {final_model_dir}")

print(f"\n🎯 Model Performance by Class:")
for label_num in sorted(reverse_label_mapping.keys()):
    label_name = reverse_label_mapping[label_num]
    f1_score = test_report[label_name]['f1-score']
    print(f"  {label_name.capitalize()}: F1 = {f1_score:.4f}")

print(f"\n💡 Next Steps:")
print(f"  1. Download model files from {final_model_dir}/")
print(f"  2. Use the model for financial sentiment prediction")
print(f"  3. Consider further fine-tuning with domain-specific data")
print(f"  4. Deploy the model for production use")

print(f"\n🚀 Your FinBERT model is ready for financial sentiment analysis!")

# Show quick test example
print(f"\n🧪 Quick Test Example:")
test_text = "The stock price increased significantly after the earnings announcement."
inputs = tokenizer(test_text, return_tensors="pt", truncation=True, padding=True, max_length=optimal_max_length)

with torch.no_grad():
    outputs = model(**inputs)
    predictions = torch.nn.functional.softmax(outputs.logits, dim=-1)
    predicted_class = torch.argmax(predictions, dim=-1).item()
    confidence = predictions[0][predicted_class].item()

predicted_sentiment = reverse_label_mapping[predicted_class]
print(f"  Text: {test_text}")
print(f"  Predicted sentiment: {predicted_sentiment}")
print(f"  Confidence: {confidence:.4f}")

print(f"\n✨ Training pipeline completed! ✨")